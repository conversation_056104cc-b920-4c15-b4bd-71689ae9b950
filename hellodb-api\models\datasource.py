import enum
from extensions.ext_database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from sqlalchemy.sql import func
import sqlalchemy_utils as su


encryption_key = 'ASDFGHJKWERTYUIZXCVBNM1234567890QWERTYUIOP'

class Datasource(db.Model):
    __tablename__ = 'datasources'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment='主键')
    name = db.Column(db.String(255), nullable=False, comment='数据源名称')
    type = db.Column(db.String(50), nullable=False, comment='数据源类型')
    host = db.Column(db.String(255), nullable=True, comment='数据源主机地址')
    port = db.Column(db.Integer, nullable=True, comment='数据源端口')
    database = db.Column(db.String(255), nullable=True, comment='数据源数据库名称')
    username = db.Column(db.String(255), nullable=False, comment='数据源用户名')
    # password = db.Column(db.String(255), nullable=True, comment='数据源密码')
    password = db.Column(su.EncryptedType(db.String, encryption_key), nullable=True, comment='数据源密码加密字段')
    dsn = db.Column(db.String(255), nullable=True, comment='数据源DSN')
    ssl = db.Column(db.Boolean, nullable=True, default=False, comment='是否启用SSL连接')
    tds_version = db.Column(db.String(50), nullable=True, comment='数据源TDS版本')
    created_at = db.Column(db.DateTime, server_default=func.now(), comment='创建时间')
    created_by = db.Column(db.String(50), nullable=False, comment='创建人', server_default='system')
    updated_at = db.Column(db.DateTime, server_default=func.now(), onupdate=db.func.current_timestamp(), comment='更新时间')
    updated_by = db.Column(db.String(50), nullable=False, comment='更新人', server_default='system')

    def __repr__(self):
        return f'<Datasource {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'type': self.type,
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username,
            'password': self.password,
            'dsn': self.dsn,
            'tds_version': self.tds_version,
            'ssl': self.ssl,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'created_by': self.created_by,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'updated_by': self.updated_by
        }

class DataSourceApp(db.Model):
    """数据源应用表"""
    __tablename__ = 'data_source_app'

    class Status(str, enum.Enum):
        ACTIVE = 'active'
        INACTIVE = 'inactive'
    
    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'), comment='主键')
    name = db.Column(db.String(50), nullable=False, comment='应用名称')
    alias = db.Column(db.String(50), nullable=False, comment='应用别名')
    description = db.Column(db.Text, nullable=True, comment='应用描述')
    datasource_id = db.Column(UUID, db.ForeignKey('datasources.id'), nullable=False, comment='数据源ID')
    datasource_type = db.Column(db.String(50), nullable=False, server_default='mysql', comment='数据源类型')
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    created_by = db.Column(db.String(50), nullable=False, server_default='system', comment='创建人')
    updated_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='更新时间')
    updated_by = db.Column(db.String(50), nullable=False, server_default='system', comment='更新人')
    initial_prompt = db.Column(db.Text, nullable=True, comment='初始提示词')
    app_key = db.Column(db.String(50), nullable=True, server_default=db.text('uuid_generate_v4()'), comment='应用密钥')
    status = db.Column(db.String(50), nullable=False, server_default='active', comment='状态')
    check_results = db.Column(db.JSON, nullable=True, comment='检测结果')
    last_check_time = db.Column(db.DateTime, nullable=True, comment='上次检测时间') 

    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'alias': self.alias,
            'description': self.description,
            'initial_prompt': self.initial_prompt,
            'datasource_id': str(self.datasource_id),
            'datasource_type': self.datasource_type,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'created_by': self.created_by,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'updated_by': self.updated_by,
            'app_key': self.app_key,
            'status': self.status,
            'check_results': self.check_results,
            'last_check_time': self.last_check_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_check_time else None
        }
    

class DataSourceAppStatistics(db.Model):
    """数据源应用统计表"""
    __tablename__ = 'data_source_app_statistics'
    __table_args__ = (
        db.Index('ix_data_source_app_statistics_app_id', 'app_id'),
    )

    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'), comment='主键')
    app_id = db.Column(UUID, db.ForeignKey('data_source_app.id'), nullable=False, comment='应用ID')
    table_count = db.Column(db.Integer, nullable=False, server_default=db.text('0'), comment='表数')
    training_data_count = db.Column(db.Integer, nullable=False, server_default=db.text('0'), comment='训练数据量')
    message_count = db.Column(db.Integer, nullable=False, server_default=db.text('0'), comment='消息数')
    success_message_count = db.Column(db.Integer, nullable=False, server_default=db.text('0'), comment='成功消息数')


    def to_dict(self):
        """转换为字典"""
        return {
            'id': str(self.id),
            'app_id': str(self.app_id),
            'table_count': self.table_count,
            'training_data_count': self.training_data_count,
            'message_count': self.message_count,
            'success_message_count': self.success_message_count
        }


class ChatdbMessage(db.Model):
    """chatdb对话日志表"""
    __tablename__ = 'chatdb_message'

    class FromSource(str, enum.Enum):
        WEB = 'web'
        API = 'api'

    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'), comment='主键')
    app_id = db.Column(UUID, db.ForeignKey('data_source_app.id'), nullable=False, comment='应用ID')
    question = db.Column(db.String(1024), nullable=False, comment='用户问题')
    answer_text = db.Column(db.Text, nullable=True, comment='回复消息')
    answer_sql = db.Column(db.Text, nullable=True, comment='回复SQL语句')
    answer_sql_valid = db.Column(db.Boolean, nullable=False, comment='SQL语句是否有效')
    answer_data = db.Column(db.Text, nullable=True, comment='回复数据')
    answer_data_row_count = db.Column(db.Integer, nullable=False, server_default=db.text('0'), comment='回复数据行数')
    answer_charts = db.Column(db.Text, nullable=True, comment='回复图表代码')
    from_source = db.Column(db.String(16), nullable=False, comment='来源')
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    created_by = db.Column(db.String(50), nullable=True, comment='创建人')
    is_recommend = db.Column(db.Boolean, nullable=False, server_default=db.text('false'), comment='是否推荐')
    recommend_at = db.Column(db.DateTime, nullable=True, comment='推荐时间')
    recommend_by = db.Column(db.String(50), nullable=True, comment='推荐人')

    def to_dict(self):
        return {
            'id': str(self.id),
            'app_id': self.app_id,
            'question': self.question,
            'answer_text': self.answer_text,
            'answer_sql': self.answer_sql,
            'answer_sql_valid': self.answer_sql_valid,
            'answer_data': self.answer_data,
            'answer_data_row_count': self.answer_data_row_count,
            'answer_charts': self.answer_charts,
            'from_source': self.from_source,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'is_recommend': self.is_recommend,
            'recommend_at': self.recommend_at.strftime('%Y-%m-%d %H:%M:%S') if self.recommend_at else None,
            'recommend_by': self.recommend_by
        }