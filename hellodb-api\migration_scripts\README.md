# HelloDB 向量数据库迁移工具

## 📋 概述

基于现有migration_scripts工具的生产环境迁移解决方案，支持一键迁移和验证。

## 🚀 快速开始

### 1. 安装依赖
```bash
# 进入项目目录
cd /path/to/hellodb-api

# 安装依赖
pip install -r migration_scripts/requirements.txt
```

### 2. 设置环境变量
```bash
export DASHSCOPE_API_KEY="your-production-api-key"
```

### 3. 一键迁移
```bash
# 方式1：交互式迁移（推荐）
python migration_scripts/one_click_migrate.py

# 方式2：直接全量迁移
python migration_scripts/one_click_migrate.py --mode full

# 方式3：使用部署脚本
chmod +x migration_scripts/deploy.sh
./migration_scripts/deploy.sh
```

## 📖 使用说明

### 命令行选项
```bash
# 全量迁移
python migration_scripts/one_click_migrate.py --mode full

# 迁移指定应用
python migration_scripts/one_click_migrate.py --mode specified --apps "test,testdb"

# 仅验证迁移结果
python migration_scripts/one_click_migrate.py --mode verify
```

### 现有工具说明
- `migration_tool.py` - 核心迁移工具
- `full_migration.py` - 全量迁移脚本
- `verify_migration.py` - 验证工具
- `backup_tool.py` - 备份工具

## 🏗️ 目录结构

```
migration_scripts/
├── README.md                    # 本文档
├── .gitignore                   # Git忽略文件
├── requirements.txt             # 依赖包列表
├── one_click_migrate.py         # 一键迁移脚本
├── deploy.sh                    # 部署脚本
├── migration_tool.py            # 核心迁移工具
├── full_migration.py            # 全量迁移
├── verify_migration.py          # 验证工具
├── backup_tool.py               # 备份工具
├── output/                      # 输出文件（Git忽略）
├── backups/                     # 备份文件（Git忽略）
└── test_new_system/             # 测试文件（Git忽略）
```

## 🔧 生产环境部署

### Linux/Unix环境
```bash
# 1. 上传文件到服务器
scp -r migration_scripts/ user@server:/path/to/hellodb-api/

# 2. 设置环境变量
export DASHSCOPE_API_KEY="your-production-api-key"

# 3. 执行部署
cd /path/to/hellodb-api
./migration_scripts/deploy.sh
```

### Windows环境
```cmd
# 1. 设置环境变量
set DASHSCOPE_API_KEY=your-production-api-key

# 2. 安装依赖
pip install -r migration_scripts\requirements.txt

# 3. 执行迁移
python migration_scripts\one_click_migrate.py
```

## 📊 输出文件

### 报告文件位置
- 迁移报告: `migration_scripts/output/migration_report_*.json`
- 验证报告: `migration_scripts/output/verification_report_*.json`
- 总结报告: `migration_scripts/output/*_summary.md`

### 备份文件位置
- 数据备份: `migration_scripts/backups/`

## 🐛 故障排除

### 常见问题
1. **依赖包缺失**: 运行 `pip install -r migration_scripts/requirements.txt`
2. **API连接失败**: 检查 `DASHSCOPE_API_KEY` 环境变量
3. **数据库连接失败**: 检查数据库配置和网络连接
4. **权限问题**: 确保脚本有执行权限 `chmod +x migration_scripts/deploy.sh`

### 查看日志
```bash
# 查看最新的输出文件
ls -la migration_scripts/output/

# 如果有日志文件
tail -f migration_scripts/output/*.log
```

## 📞 技术支持

如遇到问题：
1. 查看 `migration_scripts/output/` 目录下的报告文件
2. 检查控制台输出的错误信息
3. 联系技术支持团队

---

**版本**: 1.0.0  
**基于**: 现有migration_scripts工具  
**更新时间**: 2025-07-21
