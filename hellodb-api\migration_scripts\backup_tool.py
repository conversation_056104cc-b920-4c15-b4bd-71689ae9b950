#!/usr/bin/env python3
"""
ChromaDB向量数据备份工具
"""
import os
import sys
import json
import shutil
import hashlib
import chromadb
from chromadb.config import Settings
from datetime import datetime

class VectorBackupTool:
    def __init__(self, backup_base_dir='migration_scripts/backups'):
        self.backup_base_dir = backup_base_dir
        self.backup_log = []
        
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        self.backup_log.append({
            'timestamp': timestamp,
            'level': level,
            'message': message
        })
    
    def calculate_checksum(self, data):
        """计算数据校验码"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        elif isinstance(data, dict):
            data = json.dumps(data, sort_keys=True).encode('utf-8')
        return hashlib.md5(data).hexdigest()
    
    def backup_single_app(self, app_alias):
        """备份单个应用的向量数据"""
        self.log(f"开始备份应用: {app_alias}")
        
        old_path = f'/data/chroma/{app_alias}'
        if not os.path.exists(old_path):
            self.log(f"应用 {app_alias} 的向量数据不存在，跳过备份", "WARNING")
            return None
        
        # 创建备份目录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = f'{self.backup_base_dir}/{timestamp}/{app_alias}'
        os.makedirs(backup_dir, exist_ok=True)
        
        try:
            # 连接到ChromaDB
            client = chromadb.PersistentClient(
                path=old_path,
                settings=Settings(anonymized_telemetry=False)
            )
            
            backup_data = {
                'app_alias': app_alias,
                'backup_time': timestamp,
                'source_path': old_path,
                'collections': {}
            }
            
            collections = ['sql', 'ddl', 'documentation']
            total_docs = 0
            
            for collection_name in collections:
                try:
                    collection = client.get_collection(collection_name)
                    data = collection.get()
                    
                    collection_backup = {
                        'count': collection.count(),
                        'documents': data.get('documents', []),
                        'metadatas': data.get('metadatas', []),
                        'ids': data.get('ids', [])
                    }
                    
                    # 计算校验码
                    collection_backup['checksum'] = self.calculate_checksum(collection_backup)
                    
                    backup_data['collections'][collection_name] = collection_backup
                    total_docs += collection_backup['count']
                    
                    self.log(f"备份 {collection_name} collection: {collection_backup['count']} 个文档")
                    
                except Exception as e:
                    self.log(f"备份 {collection_name} collection 失败: {str(e)}", "ERROR")
                    backup_data['collections'][collection_name] = {'error': str(e)}
            
            # 保存备份数据
            backup_file = f'{backup_dir}/backup_data.json'
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            # 复制原始ChromaDB文件
            try:
                chroma_backup_dir = f'{backup_dir}/chroma_files'
                shutil.copytree(old_path, chroma_backup_dir)
                self.log(f"复制ChromaDB文件到: {chroma_backup_dir}")
            except Exception as e:
                self.log(f"复制ChromaDB文件失败: {str(e)}", "WARNING")
            
            # 生成备份清单
            manifest = {
                'app_alias': app_alias,
                'backup_time': timestamp,
                'total_documents': total_docs,
                'backup_file': backup_file,
                'chroma_backup_dir': chroma_backup_dir if 'chroma_backup_dir' in locals() else None,
                'collections_summary': {
                    name: info.get('count', 0) if isinstance(info, dict) else 0
                    for name, info in backup_data['collections'].items()
                }
            }
            
            manifest_file = f'{backup_dir}/manifest.json'
            with open(manifest_file, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, ensure_ascii=False, indent=2)
            
            self.log(f"应用 {app_alias} 备份完成: {total_docs} 个文档")
            return manifest
            
        except Exception as e:
            self.log(f"备份应用 {app_alias} 失败: {str(e)}", "ERROR")
            return None
    
    def backup_all_apps(self, app_list=None):
        """备份所有应用"""
        if app_list is None:
            # 扫描所有应用
            base_path = '/data/chroma'
            if not os.path.exists(base_path):
                self.log(f"ChromaDB目录不存在: {base_path}", "ERROR")
                return None
            
            app_list = [d for d in os.listdir(base_path) 
                       if os.path.isdir(os.path.join(base_path, d))]
        
        self.log(f"开始备份 {len(app_list)} 个应用")
        
        backup_summary = {
            'backup_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_apps': len(app_list),
            'success_count': 0,
            'error_count': 0,
            'manifests': []
        }
        
        for app_alias in app_list:
            try:
                manifest = self.backup_single_app(app_alias)
                if manifest:
                    backup_summary['manifests'].append(manifest)
                    backup_summary['success_count'] += 1
                else:
                    backup_summary['error_count'] += 1
            except Exception as e:
                self.log(f"备份应用 {app_alias} 时发生异常: {str(e)}", "ERROR")
                backup_summary['error_count'] += 1
        
        # 保存总体备份报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'{self.backup_base_dir}/backup_report_{timestamp}.json'
        
        report = {
            'backup_summary': backup_summary,
            'backup_log': self.backup_log
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.log(f"备份完成: 成功={backup_summary['success_count']}, 失败={backup_summary['error_count']}")
        self.log(f"备份报告已保存到: {report_file}")
        
        return backup_summary
    
    def verify_backup(self, manifest_file):
        """验证备份完整性"""
        self.log(f"验证备份: {manifest_file}")
        
        try:
            with open(manifest_file, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            backup_dir = os.path.dirname(manifest_file)
            backup_data_file = f"{backup_dir}/backup_data.json"
            
            if not os.path.exists(backup_data_file):
                self.log(f"备份数据文件不存在: {backup_data_file}", "ERROR")
                return False
            
            with open(backup_data_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # 验证文档数量
            total_docs = 0
            for collection_name, collection_data in backup_data['collections'].items():
                if isinstance(collection_data, dict) and 'count' in collection_data:
                    total_docs += collection_data['count']
            
            if total_docs != manifest['total_documents']:
                self.log(f"文档数量不匹配: 清单={manifest['total_documents']}, 实际={total_docs}", "ERROR")
                return False
            
            self.log(f"备份验证成功: {manifest['app_alias']}")
            return True
            
        except Exception as e:
            self.log(f"验证备份失败: {str(e)}", "ERROR")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ChromaDB向量数据备份工具')
    parser.add_argument('--app', type=str, help='备份单个应用的别名')
    parser.add_argument('--all', action='store_true', help='备份所有应用')
    parser.add_argument('--verify', type=str, help='验证指定的备份清单文件')
    parser.add_argument('--backup-dir', type=str, default='migration_scripts/backups', help='备份目录')
    
    args = parser.parse_args()
    
    tool = VectorBackupTool(backup_base_dir=args.backup_dir)
    
    if args.verify:
        tool.verify_backup(args.verify)
    elif args.app:
        tool.backup_single_app(args.app)
    elif args.all:
        tool.backup_all_apps()
    else:
        # 默认备份有数据的应用
        scan_file = 'migration_scripts/output/vector_data_scan_results.json'
        if os.path.exists(scan_file):
            with open(scan_file, 'r', encoding='utf-8') as f:
                scan_data = json.load(f)
            
            app_list = []
            for app_alias, app_info in scan_data.get('/data/chroma', {}).items():
                if isinstance(app_info.get('data'), dict):
                    total_docs = sum(count for count in app_info['data'].values() 
                                   if isinstance(count, int))
                    if total_docs > 0:
                        app_list.append(app_alias)
            
            if app_list:
                tool.log(f"发现 {len(app_list)} 个有数据的应用需要备份")
                tool.backup_all_apps(app_list)
            else:
                tool.log("没有发现需要备份的应用")
        else:
            tool.log(f"扫描结果文件不存在: {scan_file}", "ERROR")

if __name__ == "__main__":
    main()
