#!/bin/bash

# HelloDB向量数据库迁移 - 生产环境部署脚本
# 基于现有migration_scripts工具
# 版本: 1.0.0
# 日期: 2025-07-21

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_banner() {
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                HelloDB 向量数据库迁移部署脚本                    ║"
    echo "║                                                              ║"
    echo "║  基于现有migration_scripts工具                                ║"
    echo "║  版本: 1.0.0                                                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo
}

# 检查环境
check_environment() {
    print_info "检查环境..."
    
    # 检查当前目录
    if [ ! -f "migration_scripts/migration_tool.py" ]; then
        print_error "请在hellodb-api目录下运行此脚本"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $python_version"
    
    # 检查依赖
    print_info "检查Python依赖..."
    if [ -f "migration_scripts/requirements.txt" ]; then
        pip3 install -r migration_scripts/requirements.txt
    else
        pip3 install chromadb sqlalchemy psycopg2-binary
    fi
    
    print_success "依赖检查完成"
}

# 检查环境变量
check_env_vars() {
    print_info "检查环境变量..."
    
    if [ -z "$DASHSCOPE_API_KEY" ]; then
        print_warning "未设置DASHSCOPE_API_KEY环境变量"
        read -p "请输入DashScope API密钥: " api_key
        export DASHSCOPE_API_KEY="$api_key"
    fi
    
    print_success "环境变量检查完成"
}

# 执行迁移
run_migration() {
    print_info "开始执行迁移..."
    
    echo "请选择迁移模式:"
    echo "1) 全量迁移所有应用（推荐）"
    echo "2) 迁移指定应用"
    echo "3) 仅验证现有迁移"
    echo "4) 退出"
    
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            print_info "执行全量迁移..."
            python3 migration_scripts/one_click_migrate.py --mode full
            ;;
        2)
            read -p "请输入应用别名（逗号分隔）: " apps
            print_info "迁移指定应用: $apps"
            python3 migration_scripts/one_click_migrate.py --mode specified --apps "$apps"
            ;;
        3)
            print_info "验证现有迁移..."
            python3 migration_scripts/one_click_migrate.py --mode verify
            ;;
        4)
            print_info "退出部署脚本"
            exit 0
            ;;
        *)
            print_error "无效选择"
            exit 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        print_success "迁移执行完成"
    else
        print_error "迁移执行失败"
        exit 1
    fi
}

# 显示结果
show_results() {
    print_info "迁移结果文件:"
    
    # 显示输出目录中的文件
    if [ -d "migration_scripts/output" ]; then
        latest_files=$(ls -t migration_scripts/output/ 2>/dev/null | head -5)
        if [ -n "$latest_files" ]; then
            echo "$latest_files" | while read file; do
                print_success "  migration_scripts/output/$file"
            done
        fi
    fi
}

# 主函数
main() {
    print_banner
    
    # 执行检查步骤
    check_environment
    check_env_vars
    
    # 执行迁移
    run_migration
    
    # 显示结果
    show_results
    
    print_success "部署脚本执行完成！"
    echo
    echo "🎉 迁移任务完成！"
    echo "📋 请查看输出文件了解详细信息"
    echo "🔍 建议进行用户验收测试确保系统正常运行"
}

# 捕获中断信号
trap 'print_error "脚本被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
