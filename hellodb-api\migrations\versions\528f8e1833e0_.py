"""empty message

Revision ID: 528f8e1833e0
Revises: 66c53ffca33b
Create Date: 2025-06-12 09:31:10.224384

"""
from alembic import op
import sqlalchemy as sa
import sqlalchemy_utils  # 自动添加的依赖导入


# revision identifiers, used by Alembic.
revision = '528f8e1833e0'
down_revision = '66c53ffca33b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('data_source_app', schema=None) as batch_op:
        batch_op.add_column(sa.Column('check_results', sa.JSON(), nullable=True, comment='检测结果'))
        batch_op.add_column(sa.Column('last_check_time', sa.DateTime(), nullable=True, comment='上次检测时间'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('data_source_app', schema=None) as batch_op:
        batch_op.drop_column('last_check_time')
        batch_op.drop_column('check_results')

    # ### end Alembic commands ###
