import os

import dotenv

dotenv.load_dotenv()

DEFAULTS = {
    'EDITION': 'SELF_HOSTED',
    'DB_USERNAME': 'hellodb',
    'DB_PASSWORD': 'hellodb#2025',
    'DB_HOST': 'localhost',
    'DB_PORT': '5432',
    'DB_DATABASE': 'hellodb',
    'DB_CHARSET': '',
    'REDIS_HOST': 'localhost',
    'REDIS_PORT': '6379',
    'REDIS_DB': '0',
    'REDIS_PASSWORD': '',
    'REDIS_USE_SSL': 'False',
    'OAUTH_REDIRECT_PATH': '/console/api/oauth/authorize',
    'OAUTH_REDIRECT_INDEX_PATH': '/',
    'CONSOLE_WEB_URL': 'https://cloud.dify.ai',
    'CONSOLE_API_URL': 'https://cloud.dify.ai',
    'SERVICE_API_URL': 'https://api.dify.ai',
    'APP_WEB_URL': 'https://udify.app',
    'FILES_URL': '',
    'S3_ADDRESS_STYLE': 'auto',
    'STORAGE_TYPE': 'local',
    'STORAGE_LOCAL_PATH': '/data/uploads',
    'CHECK_UPDATE_URL': 'https://updates.dify.ai',
    'DEPLOY_ENV': 'PRODUCTION',
    'SQLALCHEMY_POOL_SIZE': 30,
    'SQLALCHEMY_MAX_OVERFLOW': 10,
    'SQLALCHEMY_POOL_RECYCLE': 3600,
    'SQLALCHEMY_ECHO': 'False',
    'SENTRY_TRACES_SAMPLE_RATE': 1.0,
    'SENTRY_PROFILES_SAMPLE_RATE': 1.0,
    'WEAVIATE_GRPC_ENABLED': 'True',
    'WEAVIATE_BATCH_SIZE': 100,
    'QDRANT_CLIENT_TIMEOUT': 20,
    'CELERY_BACKEND': 'redis://localhost:6379/1',
    'CELERY_BROKER_URL': 'redis://localhost:6379/1',
    'CELERY_RESULT_BACKEND': 'redis://localhost:6379/1',
    'BROKER_USE_SSL': 'False',
    'CELERY_USE_SENTINEL': 'False',
    'CELERY_SENTINEL_MASTER_NAME': 'master',
    'CELERY_SENTINEL_SOCKET_TIMEOUT': 20,
    'CELERY_WORKER_CLASS': 'gevent',
    'CELERY_WORKER_AMOUNT': 1,
    'CELERY_QUEUES': 'chatdb',
    'LOG_LEVEL': 'INFO',
    'LOG_FILE': '/app/logs/api.log',
    'LOG_FORMAT': '%(asctime)s.%(msecs)03d %(levelname)s [%(threadName)s] [%(filename)s:%(lineno)d] - %(message)s',
    'LOG_DATEFORMAT': '%Y-%m-%d %H:%M:%S',
   
    'CLEAN_DAY_SETTING': 30,
    'UPLOAD_FILE_SIZE_LIMIT': 15,
    'UPLOAD_FILE_BATCH_LIMIT': 5,
    'UPLOAD_IMAGE_FILE_SIZE_LIMIT': 10,
    'OUTPUT_MODERATION_BUFFER_SIZE': 300,
    'MULTIMODAL_SEND_IMAGE_FORMAT': 'base64',
    'INVITE_EXPIRY_HOURS': 72,
    'BILLING_ENABLED': 'False',
    'CAN_REPLACE_LOGO': 'False',
    'ETL_TYPE': 'dify',
    'KEYWORD_STORE': 'jieba',
    'BATCH_UPLOAD_LIMIT': 20,
    'CODE_EXECUTION_ENDPOINT': 'http://sandbox:8194',
    'CODE_EXECUTION_API_KEY': 'dify-sandbox',
    'TOOL_ICON_CACHE_MAX_AGE': 3600,
    'MILVUS_DATABASE': 'default',
    'KEYWORD_DATA_SOURCE_TYPE': 'database',
    'ENTERPRISE_ENABLED': 'False',
    'XINFERENCE_URL': 'http://127.0.0.1:9997',
    'XINFERENCE_EMBEDDING_MODEL_NAME': 'bce-embedding-base_v1',
    'SECRET_KEY': 'xcdsxcwedf90345ur4356t4rgergfreg',
    'LOCAL_DATA_DIR': 'D:\\workspace\\me\\hellodb\\docker\\volumes\\app\\data',
    'DIFY_URL': 'http://127.0.0.1:8888',
    'DIFY_API_URL': 'http://127.0.0.1:8888/v1',
    ## 'DIFY_DATASET_API_KEY': 'dataset-GEMxFCjJ0f9Gx6wnaVdPxjXl',
    'DIFY_DATASET_API_KEY': 'dataset-2Tll5euH2NCh0oFJxQmNWLep',
    'STORAGE_LOCAL_URL': 'http://127.0.0.1/uploads/',
    'CAS_ENABLE': 'False',
    'CAS_SERVER_URL': 'https://iam.xnjz.com/cas/',
    'CAS_SERVICE_URL': 'http://127.0.0.1:5000/callback',
    'FRONTEND_LOGIN_URL': 'http://127.0.0.1:6678/login',
    'VANNA_LLM_MODEL': 'qwen-plus',
    'VANNA_LLM_API_KEY': 'sk-c4793b0c284e448ba1a611aa6f424062',
    'VANNA_LLM_BASE_URL': 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    'ALIYUN_SMS_ACCESS_KEY_ID': 'LTAI5tQebFghNEWJbtNMbV8i',
    'ALIYUN_SMS_ACCESS_KEY_SECRET': '******************************',
    'ALIYUN_SMS_SIGN_NAME': '纳信易',
    'ALIYUN_SMS_TEMPLATE_CODE': 'SMS_482730046',
}


def get_env(key):
    return os.environ.get(key, DEFAULTS.get(key))


def get_bool_env(key):
    value = get_env(key)
    return value.lower() == 'true' if value is not None else False


def get_cors_allow_origins(env, default):
    cors_allow_origins = []
    if get_env(env):
        for origin in get_env(env).split(','):
            cors_allow_origins.append(origin)
    else:
        cors_allow_origins = [default]

    return cors_allow_origins


class Config:
    """Application configuration class."""

    def __init__(self):
        # ------------------------
        # General Configurations.
        # ------------------------
        self.CURRENT_VERSION = "0.0.1"
        self.COMMIT_SHA = get_env('COMMIT_SHA')
        self.EDITION = get_env('EDITION')
        self.DEPLOY_ENV = get_env('DEPLOY_ENV')
        self.TESTING = False
        self.LOG_LEVEL = get_env('LOG_LEVEL')
        self.LOG_FILE = get_env('LOG_FILE')
        self.LOG_FORMAT = get_env('LOG_FORMAT')
        self.LOG_DATEFORMAT = get_env('LOG_DATEFORMAT')

        # Your App secret key will be used for securely signing the session cookie
        # Make sure you are changing this key for your deployment with a strong key.
        # You can generate a strong key using `openssl rand -base64 42`.
        # Alternatively you can set it with `SECRET_KEY` environment variable.
        self.SECRET_KEY = get_env('SECRET_KEY')


        # check update url
        self.CHECK_UPDATE_URL = get_env('CHECK_UPDATE_URL')

        # ------------------------
        # Database Configurations.
        # ------------------------
        db_credentials = {
            key: get_env(key) for key in
            ['DB_USERNAME', 'DB_PASSWORD', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_CHARSET']
        }

        db_extras = f"?client_encoding={db_credentials['DB_CHARSET']}" if db_credentials['DB_CHARSET'] else ""

        self.SQLALCHEMY_DATABASE_URI = f"postgresql://{db_credentials['DB_USERNAME']}:{db_credentials['DB_PASSWORD']}@{db_credentials['DB_HOST']}:{db_credentials['DB_PORT']}/{db_credentials['DB_DATABASE']}{db_extras}"
        self.SQLALCHEMY_ENGINE_OPTIONS = {
            'pool_size': int(get_env('SQLALCHEMY_POOL_SIZE')),
            'max_overflow': int(get_env('SQLALCHEMY_MAX_OVERFLOW')),
            'pool_recycle': int(get_env('SQLALCHEMY_POOL_RECYCLE'))
        }

        self.SQLALCHEMY_ECHO = get_bool_env('SQLALCHEMY_ECHO')

        # ------------------------
        # DB Configurations.
        # ------------------------
        self.DB_HOST = get_env('DB_HOST')
        self.DB_PORT = get_env('DB_PORT')
        self.DB_USERNAME = get_env('DB_USERNAME')
        self.DB_DATABASE = get_env('DB_DATABASE')
        self.DB_PASSWORD = get_env('DB_PASSWORD')
        self.DB_CHARSET = get_env('DB_CHARSET')

        # ------------------------
        # Redis Configurations.
        # ------------------------
        self.REDIS_HOST = get_env('REDIS_HOST')
        self.REDIS_PORT = get_env('REDIS_PORT')
        self.REDIS_USERNAME = get_env('REDIS_USERNAME')
        self.REDIS_PASSWORD = get_env('REDIS_PASSWORD')
        self.REDIS_DB = get_env('REDIS_DB')
        self.REDIS_USE_SSL = get_bool_env('REDIS_USE_SSL')


        # 阿里云短信服务配置
        self.ALIYUN_SMS_ACCESS_KEY_ID = get_env('ALIYUN_SMS_ACCESS_KEY_ID')
        self.ALIYUN_SMS_ACCESS_KEY_SECRET = get_env('ALIYUN_SMS_ACCESS_KEY_SECRET')
        self.ALIYUN_SMS_SIGN_NAME = get_env('ALIYUN_SMS_SIGN_NAME')  # 短信签名
        self.ALIYUN_SMS_TEMPLATE_CODE = get_env('ALIYUN_SMS_TEMPLATE_CODE')  # 短信模板CODE

        # ------------------------
        # Vector Store Configurations.
        # Currently, only support: qdrant, milvus, zilliz, weaviate, relyt
        # ------------------------
        self.VECTOR_STORE = get_env('VECTOR_STORE')
        self.KEYWORD_STORE = get_env('KEYWORD_STORE')

        # relyt settings
        self.RELYT_HOST = get_env('RELYT_HOST')
        self.RELYT_PORT = get_env('RELYT_PORT')
        self.RELYT_USER = get_env('RELYT_USER')
        self.RELYT_PASSWORD = get_env('RELYT_PASSWORD')
        self.RELYT_DATABASE = get_env('RELYT_DATABASE')

        # ------------------------
        # Mail Configurations.
        # ------------------------
        self.MAIL_TYPE = get_env('MAIL_TYPE')
        self.MAIL_DEFAULT_SEND_FROM = get_env('MAIL_DEFAULT_SEND_FROM')
        self.RESEND_API_KEY = get_env('RESEND_API_KEY')
        self.RESEND_API_URL = get_env('RESEND_API_URL')
        # SMTP settings
        self.SMTP_SERVER = get_env('SMTP_SERVER')
        self.SMTP_PORT = get_env('SMTP_PORT')
        self.SMTP_USERNAME = get_env('SMTP_USERNAME')
        self.SMTP_PASSWORD = get_env('SMTP_PASSWORD')
        self.SMTP_USE_TLS = get_bool_env('SMTP_USE_TLS')
        
        # ------------------------
        # Workspace Configurations.
        # ------------------------
        self.INVITE_EXPIRY_HOURS = int(get_env('INVITE_EXPIRY_HOURS'))

        # ------------------------
        # Sentry Configurations.
        # ------------------------
        self.SENTRY_DSN = get_env('SENTRY_DSN')
        self.SENTRY_TRACES_SAMPLE_RATE = float(get_env('SENTRY_TRACES_SAMPLE_RATE'))
        self.SENTRY_PROFILES_SAMPLE_RATE = float(get_env('SENTRY_PROFILES_SAMPLE_RATE'))

        # ------------------------
        # Business Configurations.
        # ------------------------

        # multi model send image format, support base64, url, default is base64
        self.MULTIMODAL_SEND_IMAGE_FORMAT = get_env('MULTIMODAL_SEND_IMAGE_FORMAT')

        # Dataset Configurations.
        self.CLEAN_DAY_SETTING = get_env('CLEAN_DAY_SETTING')

        # File upload Configurations.
        self.UPLOAD_FILE_SIZE_LIMIT = int(get_env('UPLOAD_FILE_SIZE_LIMIT'))
        self.UPLOAD_FILE_BATCH_LIMIT = int(get_env('UPLOAD_FILE_BATCH_LIMIT'))
        self.UPLOAD_IMAGE_FILE_SIZE_LIMIT = int(get_env('UPLOAD_IMAGE_FILE_SIZE_LIMIT'))

        # Moderation in app Configurations.
        self.OUTPUT_MODERATION_BUFFER_SIZE = int(get_env('OUTPUT_MODERATION_BUFFER_SIZE'))


        self.BATCH_UPLOAD_LIMIT = get_env('BATCH_UPLOAD_LIMIT')

        self.CODE_EXECUTION_ENDPOINT = get_env('CODE_EXECUTION_ENDPOINT')
        self.CODE_EXECUTION_API_KEY = get_env('CODE_EXECUTION_API_KEY')

        self.API_COMPRESSION_ENABLED = get_bool_env('API_COMPRESSION_ENABLED')
        self.TOOL_ICON_CACHE_MAX_AGE = get_env('TOOL_ICON_CACHE_MAX_AGE')

        self.KEYWORD_DATA_SOURCE_TYPE = get_env('KEYWORD_DATA_SOURCE_TYPE')
        self.ENTERPRISE_ENABLED = get_bool_env('ENTERPRISE_ENABLED')

        # xinference相关配置
        self.XINFERENCE_URL = get_env('XINFERENCE_URL')
        self.XINFERENCE_EMBEDDING_MODEL_NAME = get_env('XINFERENCE_EMBEDDING_MODEL_NAME')

        self.SQLALCHEMY_DATABASE_URI_POSTGRES = f"postgresql://{db_credentials['DB_USERNAME']}:{db_credentials['DB_PASSWORD']}@{db_credentials['DB_HOST']}:{db_credentials['DB_PORT']}/{db_credentials['DB_DATABASE']}{db_extras}"
        self.SQLALCHEMY_DATABASE_URI_MYSQL = ''

        self.OPENAI_API_KEY = get_env('OPENAI_API_KEY')

        self.LOCAL_DATA_DIR = get_env('LOCAL_DATA_DIR')
        self.DIFY_URL = get_env('DIFY_URL')
        self.DIFY_API_URL = get_env('DIFY_API_URL')
        self.DIFY_DATASET_API_KEY = get_env('DIFY_DATASET_API_KEY')
        self.STORAGE_TYPE = get_env('STORAGE_TYPE')
        self.STORAGE_LOCAL_PATH = get_env('STORAGE_LOCAL_PATH')
        self.STORAGE_LOCAL_URL = get_env('STORAGE_LOCAL_URL')

        self.CAS_ENABLE = get_bool_env('CAS_ENABLE')
        self.CAS_SERVER_URL = get_env('CAS_SERVER_URL')
        self.CAS_SERVICE_URL = get_env('CAS_SERVICE_URL')
        self.FRONTEND_LOGIN_URL = get_env('FRONTEND_LOGIN_URL')

        self.VANNA_LLM_MODEL = get_env('VANNA_LLM_MODEL')
        self.VANNA_LLM_API_KEY = get_env('VANNA_LLM_API_KEY')
        self.VANNA_LLM_BASE_URL = get_env('VANNA_LLM_BASE_URL')

        self.CELERY_BACKEND = get_env('CELERY_BACKEND')
        self.CELERY_BROKER_URL = get_env('CELERY_BROKER_URL')
        self.CELERY_RESULT_BACKEND = get_env('CELERY_RESULT_BACKEND')
        self.BROKER_USE_SSL = get_bool_env('BROKER_USE_SSL')
        self.CELERY_USE_SENTINEL = get_bool_env('CELERY_USE_SENTINEL')
        self.CELERY_SENTINEL_MASTER_NAME = get_env('CELERY_SENTINEL_MASTER_NAME')
        self.CELERY_SENTINEL_SOCKET_TIMEOUT = int(get_env('CELERY_SENTINEL_SOCKET_TIMEOUT'))
        self.CELERY_WORKER_CLASS = get_env('CELERY_WORKER_CLASS')
        self.CELERY_WORKER_AMOUNT = int(get_env('CELERY_WORKER_AMOUNT'))
        self.CELERY_QUEUES = get_env('CELERY_QUEUES')
