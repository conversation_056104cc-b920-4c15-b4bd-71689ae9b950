#!/usr/bin/env python3
"""
应用诊断脚本 - 分析应用数量差异
"""
import os
import sys
import json

def check_database_apps():
    """检查数据库中的应用"""
    try:
        from sqlalchemy import create_engine, text
        
        # 构建数据库连接URL
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'hellodb')
        db_user = os.getenv('DB_USER', 'postgres')
        db_password = os.getenv('DB_PASSWORD', 'password')
        
        db_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        engine = create_engine(db_url)
        with engine.connect() as conn:
            # 查看所有状态的应用数量
            status_result = conn.execute(text("""
                SELECT status, COUNT(*) as count
                FROM data_source_app 
                GROUP BY status
                ORDER BY status
            """))
            status_counts = status_result.fetchall()
            
            print("📊 数据库中应用状态分布:")
            total_db_apps = 0
            for status, count in status_counts:
                print(f"   {status}: {count} 个")
                total_db_apps += count
            print(f"   总计: {total_db_apps} 个")
            
            # 获取所有应用的详细信息
            all_result = conn.execute(text("""
                SELECT alias, status, created_at
                FROM data_source_app 
                ORDER BY created_at DESC
            """))
            all_apps = all_result.fetchall()
            
            print(f"\n📋 最近创建的10个应用:")
            for i, (alias, status, created_at) in enumerate(all_apps[:10]):
                print(f"   {i+1}. {alias} ({status}) - {created_at}")
            
            # 获取活跃应用列表
            active_result = conn.execute(text("""
                SELECT DISTINCT alias 
                FROM data_source_app 
                WHERE status = 'active' 
                ORDER BY alias
            """))
            active_apps = [row[0] for row in active_result.fetchall()]
            
            return {
                'total_apps': total_db_apps,
                'status_counts': dict(status_counts),
                'active_apps': active_apps,
                'all_apps': [(alias, status) for alias, status, _ in all_apps]
            }
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return None

def check_scan_file():
    """检查扫描文件"""
    scan_file = 'migration_scripts/output/vector_data_scan_results.json'
    if not os.path.exists(scan_file):
        print(f"❌ 扫描文件不存在: {scan_file}")
        return None
    
    try:
        with open(scan_file, 'r', encoding='utf-8') as f:
            scan_data = json.load(f)
        
        scan_apps = list(scan_data.get('/data/chroma', {}).keys())
        print(f"📁 扫描文件中的应用数量: {len(scan_apps)}")
        
        # 显示前10个应用
        print(f"📋 扫描文件中的前10个应用:")
        for i, app in enumerate(scan_apps[:10]):
            print(f"   {i+1}. {app}")
        
        return {
            'scan_apps': scan_apps,
            'scan_count': len(scan_apps)
        }
        
    except Exception as e:
        print(f"❌ 读取扫描文件失败: {str(e)}")
        return None

def check_vector_directories():
    """检查向量存储目录"""
    old_path = '/data/chroma'
    new_path = '/data/hellodb/chroma'
    
    print(f"📁 检查向量存储目录:")
    
    # 检查旧目录
    if os.path.exists(old_path):
        try:
            old_dirs = [d for d in os.listdir(old_path) if os.path.isdir(os.path.join(old_path, d))]
            print(f"   旧目录 {old_path}: {len(old_dirs)} 个应用")
            print(f"   前10个: {old_dirs[:10]}")
        except Exception as e:
            print(f"   旧目录访问失败: {str(e)}")
    else:
        print(f"   旧目录 {old_path}: 不存在")
    
    # 检查新目录
    if os.path.exists(new_path):
        try:
            new_dirs = [d for d in os.listdir(new_path) if os.path.isdir(os.path.join(new_path, d))]
            print(f"   新目录 {new_path}: {len(new_dirs)} 个应用")
            print(f"   前10个: {new_dirs[:10]}")
        except Exception as e:
            print(f"   新目录访问失败: {str(e)}")
    else:
        print(f"   新目录 {new_path}: 不存在")

def main():
    """主函数"""
    print("🔍 HelloDB应用数量诊断")
    print("=" * 50)
    
    # 检查数据库
    print("\n1. 检查数据库中的应用:")
    db_info = check_database_apps()
    
    # 检查扫描文件
    print("\n2. 检查扫描文件:")
    scan_info = check_scan_file()
    
    # 检查向量目录
    print("\n3. 检查向量存储目录:")
    check_vector_directories()
    
    # 分析差异
    print("\n4. 差异分析:")
    if db_info and scan_info:
        db_total = db_info['total_apps']
        db_active = len(db_info['active_apps'])
        scan_count = scan_info['scan_count']
        
        print(f"   数据库总应用数: {db_total}")
        print(f"   数据库活跃应用数: {db_active}")
        print(f"   扫描文件应用数: {scan_count}")
        
        if db_total == 82:
            print("   ✅ 数据库总数符合预期(82个)")
        else:
            print(f"   ⚠️  数据库总数({db_total})与预期(82)不符")
        
        if db_active != scan_count:
            print(f"   ⚠️  活跃应用数({db_active})与扫描文件({scan_count})不符")
            print("   建议: 重新运行向量数据扫描")
        
        # 找出缺失的应用
        if db_info and 'active_apps' in db_info and scan_info and 'scan_apps' in scan_info:
            db_set = set(db_info['active_apps'])
            scan_set = set(scan_info['scan_apps'])
            
            missing_in_scan = db_set - scan_set
            extra_in_scan = scan_set - db_set
            
            if missing_in_scan:
                print(f"   📋 数据库中有但扫描文件中缺失的应用({len(missing_in_scan)}个):")
                for app in sorted(list(missing_in_scan)[:10]):
                    print(f"      - {app}")
                if len(missing_in_scan) > 10:
                    print(f"      ... 还有{len(missing_in_scan)-10}个")
            
            if extra_in_scan:
                print(f"   📋 扫描文件中有但数据库中缺失的应用({len(extra_in_scan)}个):")
                for app in sorted(list(extra_in_scan)[:10]):
                    print(f"      - {app}")
    
    print("\n5. 建议解决方案:")
    if db_info:
        if db_info['total_apps'] == 82:
            print("   ✅ 数据库应用数量正确")
            if len(db_info['active_apps']) < 82:
                inactive_count = db_info['total_apps'] - len(db_info['active_apps'])
                print(f"   📋 有{inactive_count}个非活跃应用未被迁移")
                print("   建议: 检查是否需要迁移非活跃应用")
        else:
            print("   ⚠️  数据库应用数量不符合预期")
            print("   建议: 检查数据库配置和查询条件")
    
    if scan_info and db_info:
        if scan_info['scan_count'] < len(db_info['active_apps']):
            print("   📋 扫描文件不完整")
            print("   建议: 重新运行向量数据扫描")
    
    print("\n6. 修复命令:")
    print("   # 重新扫描向量数据")
    print("   python migration_scripts/scan_vector_data.py")
    print("   ")
    print("   # 如需迁移所有状态的应用，修改full_migration.py中的SQL查询")
    print("   # 将 WHERE status = 'active' 改为不加状态限制")

if __name__ == "__main__":
    main()
