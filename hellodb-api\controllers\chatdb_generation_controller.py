"""
ChatDB自然语言查询控制器

该模块提供了自然语言到SQL的转换和数据查询功能，支持智能问答和数据可视化。

主要功能：
1. SQL生成与执行
   - 自然语言转SQL：将用户问题转换为SQL查询
   - SQL执行：运行生成的SQL并获取数据
   - SQL修复：自动检测和修复SQL错误
2. 数据可视化
   - 图表生成：基于查询结果生成可视化图表
   - CSV导出：支持数据导出为CSV格式
3. 智能交互
   - 问题推荐：生成相关的推荐问题
   - 后续问题：根据当前查询生成后续问题建议
   - 历史记录：维护用户查询历史

接口列表：
- POST /api/v0/generate_questions              生成推荐问题
- POST /api/v0/generate_sql                    生成SQL语句
- POST /api/v0/run_sql/<id>                   执行SQL查询
- POST /api/v0/fix_sql                        修复SQL错误
- POST /api/v0/download_csv/<id>              下载CSV数据
- POST /api/v0/generate_plotly_figure/<id>    生成图表
- POST /api/v0/generate_followup_questions/<id> 生成后续问题
- POST /api/v0/load_question/<id>             加载历史问题
- POST /api/v0/get_question_history           获取问题历史
- POST /api/v0/generate_sql_and_data_and_plotly_figure 生成SQL并执行获取数据和图表
- POST /openapi/v0/datasource/chat            OpenAPI接口：数据源聊天

依赖：
- Flask：Web框架
- Flask-Login：用户认证
- Flask-RESTful：REST API支持
- OpenAI：大语言模型接口
- Plotly：数据可视化
- Pandas：数据处理
- Vanna：SQL生成和数据处理

Author: guoweiwei
Date: 2025-03-12
"""

from openai import OpenAI
from config import Config
from controllers import api

from functools import wraps
from controllers.api_response import ApiResponse
from controllers.wraps import Resource
from flask_restful import marshal_with, reqparse

from flask import Response, current_app as app, jsonify, request

from core.cache import MemoryCache

from core.hellodb.hellodb import Hellodb
from core.hellodb.hellodb_factory import HellodbFactory
from core.vanna.hellodb_vanna import HellodbVanna
from core.vanna.hellodb_vanna_factory import HellodbVannaFactory

from flask_login import current_user, login_required
from models.datasource import DataSourceApp, ChatdbMessage
from services.datasource_service import DatasourceService
from services.chatdb_message_service import ChatdbMessageService
from tasks.chatdb_train_database_task import train_vanna_task
from controllers.wraps import license_required

cache = MemoryCache()

def requires_cache(fields):
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            id = request.args.get('id')

            if id is None:
                return jsonify({"type": "error", "error": "No id provided"})

            for field in fields:
                if cache.get(id=id, field=field) is None:
                    return jsonify({"type": "error", "error": f"No {field} found"})

            field_values = {field: cache.get(
                id=id, field=field) for field in fields}

            # Add the id to the field_values
            field_values['id'] = id

            return f(*args, **field_values, **kwargs)
        return decorated
    return decorator


def get_app_info(appAlias: str) -> DataSourceApp:
    # 需要判断appAlias不为空（包含空字符串）
    if appAlias is not None and appAlias != '':
        appInfo = DatasourceService.get_app_by_alias(appAlias)
        return appInfo
    return None

def get_vanna_client_old(datasourceApp: DataSourceApp = None) -> HellodbVanna:
    config = Config()
    VANNA_LLM_MODEL = config.VANNA_LLM_MODEL
    VANNA_LLM_API_KEY = config.VANNA_LLM_API_KEY
    VANNA_LLM_BASE_URL = config.VANNA_LLM_BASE_URL

    if datasourceApp is None:
        path = '/data/chroma/default'
        initial_prompt = ''
    else:
        path = f'/data/chroma/{datasourceApp.alias}'
        initial_prompt = datasourceApp.initial_prompt

    vanna_config = {
        'path' : path,
        'model': VANNA_LLM_MODEL,
        'api_key': VANNA_LLM_API_KEY,
        'initial_prompt': initial_prompt,
        'language': '中文'
    }

    client = OpenAI(api_key=VANNA_LLM_API_KEY, base_url=VANNA_LLM_BASE_URL)

    vn = HellodbVannaFactory.create_vanna(datasourceApp, client=client, config=vanna_config)

    return vn


def get_vanna_client(datasourceApp: DataSourceApp = None) -> Hellodb:
    return HellodbFactory.create_hellodb(datasourceApp)


"""
Generate Questions
"""
class GenerateQuestionsApi(Resource):
    @license_required
    def post(self):

        appAlias = request.args.get('app') or request.json.get('app')
        dataSourceApp = get_app_info(appAlias)
        vn = get_vanna_client(dataSourceApp)

        # If self has an _model attribute and model=='chinook'
        if hasattr(vn, "_model") and vn._model == "chinook":
            return jsonify(
                {
                    "type": "question_list",
                    "questions": [
                        "What are the top 10 artists by sales?",
                        "What are the total sales per year by country?",
                        "Who is the top selling artist in each genre? Show the sales numbers.",
                        "How do the employees rank in terms of sales performance?",
                        "Which 5 cities have the most customers?",
                    ],
                    "header": "Here are some questions you can ask:",
                }
            )

        training_data = vn.get_training_data()

        # If training data is None or empty
        if training_data is None or len(training_data) == 0:
            return ApiResponse.error(code=500, msg="No training data found. Please add some training data first.")

        # Get the questions from the training data
        try:
            # Filter training data to only include questions where the question is not null
            questions = (
                training_data[training_data["question"].notnull()]
                .sample(5)["question"]
                .tolist()
            )

            # Temporarily this will just return an empty list
            return ApiResponse.success(
                {
                    "type": "question_list",
                    "questions": questions,
                    "header": "Here are some questions you can ask",
                }
            )
        except Exception as e:
            return ApiResponse.success(
                {
                    "type": "question_list",
                    "questions": [],
                    "header": "Go ahead and ask a question",
                }
            )


class GenerateSQLApi(Resource):
    @license_required
    def post(self):
        data = request.get_json()
        question = data.get('question')
        appAlias = data.get('app')

        dataSourceApp = get_app_info(appAlias)
        vn = get_vanna_client(dataSourceApp)
        if question is None:
            return ApiResponse.error(400, "No question provided")
        id = cache.generate_id(question=question)
        sql = vn.generate_sql(question=question, allow_llm_to_see_data=True)
        cache.set(id=id, field='question', value=question)
        cache.set(id=id, field='sql', value=sql)

        if vn.is_sql_valid(sql=sql):
            return ApiResponse.success({
                "type": "sql",
                "id": id,
                "text": sql,
            })
        else:
            return ApiResponse.success({
                "type": "text",
                "id": id,
                "text": sql,
            })


class RunSQLApi(Resource):

    @requires_cache(['sql'])
    @license_required
    def post(self, id, sql):

        appAlias = request.args.get('app') or request.json.get('app')
        dataSourceApp = get_app_info(appAlias)
        vn = get_vanna_client(dataSourceApp)

        try:
            if not vn.run_sql_is_set:
                return ApiResponse.error(400, "Please connect to a database using vn.connect_to_... in order to run SQL queries.")

            df = vn.run_sql(sql=sql)

            self.cache.set(id=id, field="df", value=df)

            return ApiResponse.success({
                "type": "df",
                "id": id,
                "df": df.head(10).to_json(orient='records', date_format='iso'),
                "should_generate_chart": self.chart and vn.should_generate_chart(df),
            }
            )

        except Exception as e:
            return ApiResponse.error(code=400, msg=str(e))


class FixSQLApi(Resource):

    @requires_cache(["question", "sql"])
    @license_required
    def post(self, id: str, question: str, sql: str):

        data = request.get_json()
        error = data.get('error')

        dataSourceApp = get_app_info(data.get('app'))
        vn = get_vanna_client(dataSourceApp)

        if error is None:
            return jsonify({"type": "error", "error": "No error provided"})

        question = f"I have an error: {error}\n\nHere is the SQL I tried to run: {sql}\n\nThis is the question I was trying to answer: {question}\n\nCan you rewrite the SQL to fix the error?"

        fixed_sql = vn.generate_sql(question=question)

        self.cache.set(id=id, field="sql", value=fixed_sql)

        return ApiResponse.success({
            "type": "sql",
            "id": id,
            "text": fixed_sql,
        }
        )


class DownloadCSVApi(Resource):

    @requires_cache(['df'])
    @license_required
    def post(self, id, df):
        csv = df.to_csv()
        return Response(
            csv,
            mimetype="text/csv",
            headers={"Content-disposition":
                     f"attachment; filename={id}.csv"})


class GenerateFollowupQuestionsApi(Resource):
    @requires_cache(['df', 'question', 'sql'])
    @license_required
    def post(self, id, df, question, sql):
        appAlias = request.args.get('app') or request.json.get('app')
        dataSourceApp = get_app_info(appAlias)
        vn = get_vanna_client(dataSourceApp)

        if self.allow_llm_to_see_data:
            followup_questions = vn.generate_followup_questions(
                question=question, sql=sql, df=df
            )
            if followup_questions is not None and len(followup_questions) > 5:
                followup_questions = followup_questions[:5]

            self.cache.set(id=id, field="followup_questions",
                           value=followup_questions)

            return ApiResponse.success({
                "type": "question_list",
                "id": id,
                "questions": followup_questions,
                "header": "Here are some followup questions you can ask:"
            })
        else:
            # Initialize followup_questions to empty list
            followup_questions = []
            cache.set(id=id, field='followup_questions',
                      value=followup_questions)
            return ApiResponse.success({
                "type": "question_list",
                "id": id,
                "questions": [],
                "header": "Followup Questions can be enabled if you set allow_llm_to_see_data=True"
            })


class LoadQuestionApi(Resource):
    @requires_cache(['question', 'sql', 'df', 'fig_json', 'followup_questions'])
    @license_required
    def post(self, id, question, sql, df, fig_json, followup_questions):
        try:
            return ApiResponse.success({
                "type": "question_cache",
                "id": id,
                "question": question,
                "sql": sql,
                "df": df.head(10).to_json(orient='records'),
                "fig": fig_json,
                "followup_questions": followup_questions,
            })
        except Exception as e:
            return ApiResponse.error(500, str(e))
        

class GetQuestionHistoryApi(Resource):
    @license_required
    def post(self):
        questions = cache.get_all(field_list=['question'])
        return ApiResponse.success({"type": "question_history", "questions": questions})


class GeneratePlotlyFigureApi(Resource):

    @requires_cache(["df", "question", "sql"])
    @license_required
    def post(self, id, df, question, sql):  # 注意参数名，通常 POST 方法不直接从 URL 路径中获取 id，除非有特定需求
        parser = reqparse.RequestParser()
        parser.add_argument('chart_instructions', type=str, required=False)
        args = parser.parse_args()

        chart_instructions = args.get('chart_instructions')

        dataSourceApp = get_app_info(args.get('app'))
        vn = get_vanna_client(dataSourceApp)

        try:
            # If chart_instructions is not set then attempt to retrieve the code from the cache
            if chart_instructions is None or len(chart_instructions) == 0:
                code = self.cache.get(id=id, field="plotly_code")
            else:
                question = f"{question}. When generating the chart, use these special instructions: {chart_instructions}"
                code = vn.generate_plotly_code(
                    question=question,
                    sql=sql,
                    df_metadata=f"Running df.dtypes gives:\n {df.dtypes}",
                )
                self.cache.set(id=id, field="plotly_code", value=code)

            fig = vn.get_plotly_figure(
                plotly_code=code, df=df, dark_mode=False)
            fig_json = fig.to_json()

            self.cache.set(id=id, field="fig_json", value=fig_json)

            return ApiResponse.success(data =
                {
                    "type": "plotly_figure",
                    "id": id,
                    "fig": fig_json,
                }
            )
        except Exception as e:
            # Print the stack trace
            import traceback
            traceback.print_exc()

            return ApiResponse.error(code=500, msg=str(e))


def _generate_sql_and_data_and_plotly_figure(chatdb_app, question, from_source=ChatdbMessage.FromSource.WEB, created_by=None, generate_charts=False):

        vn = get_vanna_client(chatdb_app)

        if question is None:
            return ApiResponse.error(400, "No question provided")
        
        original_question = question #  保留原始问题，日志记录和生成图表时需要

        id = cache.generate_id(question=question)

        # 初始化计数器和最大尝试次数
        generate_sql_attempts = 0
        run_sql_attempts = 0
        MAX_GENERATE_SQL_ATTEMPTS = 2
        MAX_RUN_SQL_ATTEMPTS = 2

        try:
            datasource = DatasourceService.get_datasource_by_id(chatdb_app.datasource_id)
            vn.connect_to_database(datasource=datasource)
        except Exception as e:
            text = f'数据库连接异常: {e.args}'
            ChatdbMessageService.insert_message_item(app_id=chatdb_app.id,
                                                        created_by=created_by,
                                                        question=original_question, 
                                                        answer_text=text, 
                                                        answer_sql=None, 
                                                        answer_sql_valid=False, 
                                                        answer_data=None, 
                                                        answer_data_row_count=0, 
                                                        answer_charts=None,
                                                        from_source=from_source)
            return ApiResponse.success(data={
                "type": "text",
                "id": id,
                "text": text,
            })


        while generate_sql_attempts < MAX_GENERATE_SQL_ATTEMPTS:
            # 生成SQL
            sql = vn.generate_sql(question=question, allow_llm_to_see_data=True)
            generate_sql_attempts += 1
            cache.set(id=id, field='question', value=question)
            cache.set(id=id, field='sql', value=sql)

            if not vn.is_sql_valid(sql=sql):
                if generate_sql_attempts >= MAX_GENERATE_SQL_ATTEMPTS:
                    text = f"SQL生成失败，已尝试{generate_sql_attempts}次。错误信息：{sql}"
                    ChatdbMessageService.insert_message_item(app_id=chatdb_app.id,
                                                             created_by=created_by,
                                                             question=original_question, 
                                                             answer_text=text, 
                                                             answer_sql=sql, 
                                                             answer_sql_valid=False, 
                                                             answer_data=None, 
                                                             answer_data_row_count=0, 
                                                             answer_charts=None,
                                                             from_source=from_source)
                    return ApiResponse.success(data={
                        "type": "text",
                        "id": id,
                        "text": text,
                    })
                question = f"I have an error.\n\nHere is the SQL I tried to run: {sql}\n\nThis is the question I was trying to answer: {question}\n\nCan you rewrite the SQL to fix the error?"
                continue

            # SQL有效，尝试执行
            while run_sql_attempts < MAX_RUN_SQL_ATTEMPTS:
                try:
                    df = vn.run_sql(sql=sql)
                    run_sql_attempts += 1
                    # SQL执行成功，跳出所有循环
                    break
                except Exception as e:
                    if run_sql_attempts >= MAX_RUN_SQL_ATTEMPTS:
                        if generate_sql_attempts >= MAX_GENERATE_SQL_ATTEMPTS:
                            # 所有重试都失败
                            text = f"达到最大重试次数。SQL生成尝试次数: {generate_sql_attempts}, SQL执行尝试次数: {run_sql_attempts}. 最后错误: {str(e)}"
                            ChatdbMessageService.insert_message_item(app_id=chatdb_app.id,
                                                             created_by=created_by,
                                                             question=original_question, 
                                                             answer_text=text, 
                                                             answer_sql=sql, 
                                                             answer_sql_valid=False, 
                                                             answer_data=None, 
                                                             answer_data_row_count=0, 
                                                             answer_charts=None,
                                                             from_source=from_source)
                            return ApiResponse.success(data={
                                "type": "text",
                                "id": id,
                                "text": f"达到最大重试次数。SQL生成尝试次数: {generate_sql_attempts}, SQL执行尝试次数: {run_sql_attempts}. 最后错误: {str(e)}"
                            })
                        # 尝试重新生成SQL
                        question = f"I have an error: {str(e)}\n\nHere is the SQL I tried to run: {sql}\n\nThis is the question I was trying to answer: {question}\n\nCan you rewrite the SQL to fix the error?"
                        break
                    run_sql_attempts += 1
                    continue

            # 如果SQL执行成功，处理结果
            echarts_code = ''
            if 'df' in locals() and df is not None:
                cache.set(id=id, field="df", value=df)

                # 生成图表
                if generate_charts and vn.should_generate_chart(df):

                    echarts_code = vn.generate_echarts_code(
                        question=original_question,
                        sql=sql,
                        df=df.head(50).to_dict(orient="records"),
                    )


                dbdata = df.head(50).to_markdown()
                
                if len(df) > 50:
                    text = f"数据查询成功！总共有{len(df)}行数据，云平台最多展示前50行数据，如需更多数据，请联系技术支持定制。"
                else:
                    text = "数据查询成功！"

                data = {
                    "type": "sql",
                    "id": id,
                    'text': text,
                    "sql": sql,
                    "data": dbdata
                }

                if echarts_code:
                    data['charts'] = echarts_code

                ChatdbMessageService.insert_message_item(app_id=chatdb_app.id, 
                                                         created_by=created_by,
                                                         question=original_question, 
                                                         answer_text=text, 
                                                         answer_sql=sql, 
                                                         answer_sql_valid=True, 
                                                         answer_data=dbdata, 
                                                         answer_data_row_count=df.shape[0], 
                                                         answer_charts=echarts_code,
                                                         from_source=from_source)

                return ApiResponse.success(data=data)

        # 如果所有尝试都失败
        text = f"SQL生成失败。SQL生成尝试次数: {generate_sql_attempts}, SQL执行尝试次数: {run_sql_attempts}。无效的SQL信息：{sql}"
        ChatdbMessageService.insert_message_item(app_id=chatdb_app.id, 
                                                         created_by=created_by,
                                                         question=original_question, 
                                                         answer_text=text, 
                                                         answer_sql=sql, 
                                                         answer_sql_valid=False, 
                                                         answer_data=None, 
                                                         answer_data_row_count=0, 
                                                         answer_charts=None,
                                                         from_source=from_source)
        return ApiResponse.success(data={
            "type": "text",
            "id": id,
            "text": text
        })


class GenerateSqlAndDataAndPlotlyFigureApi(Resource):
    '''
    生成SQL语句，并执行SQL获取数据（以markdown形式返回前20条），并根据数据生成图表
    '''
    @login_required
    @license_required
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('question', type=str, required=False)
        parser.add_argument('app', type=str, required=False)
        parser.add_argument('generate_charts', type=bool, required=False, default=False)
        args = parser.parse_args()

        question = args.get('question')
        dataSourceApp = get_app_info(args.get('app'))
        generate_charts = args.get('generate_charts')

        return _generate_sql_and_data_and_plotly_figure(dataSourceApp, question, from_source=ChatdbMessage.FromSource.WEB, created_by=current_user.id, generate_charts=generate_charts)

class OpenApiDatasourceChatApi(Resource):
    @license_required
    def post(self):

        parser = reqparse.RequestParser()
        parser.add_argument('question', type=str, required=True, help='question')
        parser.add_argument('app_key', type=str, required=True, help='app key')
        parser.add_argument('generate_charts', type=bool, required=False, default=False)
        parser.add_argument('user_id', type=str, required=False, default=None)  
        args = parser.parse_args()

        question = args.get('question')
        app_key = args.get('app_key')
        generate_charts = args.get('generate_charts')
        user_id = args.get("user_id")

        if question is None:
            return ApiResponse.error(400, "No question provided")
        if app_key is None:
            return ApiResponse.error(400, "No app_key provided")

        dataSourceApp = DatasourceService.get_app_by_appkey(app_key)

        if dataSourceApp is None:
            return ApiResponse.error(400, "app_key invalid")

        return _generate_sql_and_data_and_plotly_figure(dataSourceApp, question, ChatdbMessage.FromSource.API, created_by=user_id, generate_charts=generate_charts)
    

# Add resources to the API
api.add_resource(GenerateQuestionsApi, '/api/v0/generate_questions')
api.add_resource(GenerateSQLApi, '/api/v0/generate_sql')
api.add_resource(RunSQLApi, '/api/v0/run_sql/<string:id>')
api.add_resource(FixSQLApi, '/api/v0/fix_sql')
api.add_resource(DownloadCSVApi, '/api/v0/download_csv/<string:id>')
api.add_resource(GeneratePlotlyFigureApi,
                 '/api/v0/generate_plotly_figure/<string:id>')
api.add_resource(GenerateFollowupQuestionsApi,
                 '/api/v0/generate_followup_questions/<string:id>')
api.add_resource(LoadQuestionApi, '/api/v0/load_question/<string:id>')
api.add_resource(GetQuestionHistoryApi, '/api/v0/get_question_history')
api.add_resource(GenerateSqlAndDataAndPlotlyFigureApi, '/api/v0/generate_sql_and_data_and_plotly_figure')
api.add_resource(OpenApiDatasourceChatApi, '/openapi/v0/datasource/chat')