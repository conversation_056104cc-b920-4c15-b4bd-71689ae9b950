from flask import request
from flask_login import login_required
from flask_restful import marshal_with

from controllers.system import api
from controllers.wraps import WebApiResource
from controllers.api_response import ApiResponse
from fields.system_fields import department_fields, common_response_fields
from models.system import Department
from extensions.ext_database import db

def build_tree(departments, parent_id=None):
    """构建树形结构"""
    tree = []
    for dept in departments:
        if dept.parent_id == parent_id:
            children = build_tree(departments, dept.id)
            if children:
                dept.children = children
            tree.append(dept)
    return tree

class DepartmentListApi(WebApiResource):
    
    @marshal_with(common_response_fields)
    @login_required
    def get(self):
        """获取部门列表（树形结构）"""
        # 查询所有部门
        departments = Department.query.filter(Department.status != 'deleted').order_by(Department.sort).all()
        
        # 构建树形结构
        tree = build_tree(departments)
        
        return ApiResponse.success(data=tree)

    @marshal_with(common_response_fields)
    @login_required
    def post(self):
        """创建部门"""
        data = request.get_json()
        
        # 检查部门编码是否已存在
        if Department.query.filter_by(code=data['code']).first():
            return ApiResponse.error(code=400, msg='部门编码已存在')
            
        # 创建部门
        department = Department(
            name=data['name'],
            code=data['code'],
            leader=data.get('leader'),
            phone=data.get('phone'),
            email=data.get('email'),
            sort=data.get('sort', 0),
            parent_id=data.get('parent_id'),
            status=data.get('status', 'active')
        )
        
        db.session.add(department)
        db.session.commit()
        
        return ApiResponse.success(msg='创建成功')

class DepartmentApi(WebApiResource):
    
    @marshal_with(common_response_fields)
    @login_required
    def get(self, id):
        """获取部门详情"""
        department = Department.query.get_or_404(id)
        return ApiResponse.success(data=department)
    
    @marshal_with(common_response_fields)
    @login_required
    def put(self, id):
        """更新部门"""
        department = Department.query.get_or_404(id)
        data = request.get_json()
        
        # 检查部门编码是否已存在
        if data.get('code') and data['code'] != department.code:
            if Department.query.filter_by(code=data['code']).first():
                return ApiResponse.error(code=400, msg='部门编码已存在')
        
        # 更新部门信息
        for key, value in data.items():
            if hasattr(department, key):
                setattr(department, key, value)
                
        db.session.commit()
        return ApiResponse.success(msg='更新成功')
    
    @marshal_with(common_response_fields)
    @login_required
    def delete(self, id):
        """删除部门"""
        department = Department.query.get_or_404(id)
        
        # 检查是否有子部门
        if Department.query.filter_by(parent_id=id, status='active').first():
            return ApiResponse.error(code=400, msg='请先删除子部门')
            
        # 检查是否有关联用户
        if department.users:
            return ApiResponse.error(code=400, msg='请先移除部门下的用户')
        
        department.status = 'deleted'  # 软删除
        db.session.commit()
        return ApiResponse.success(msg='删除成功')

# 注册路由
api.add_resource(DepartmentListApi, '/api/system/departments')
api.add_resource(DepartmentApi, '/api/system/departments/<string:id>') 