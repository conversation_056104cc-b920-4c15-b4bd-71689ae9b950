"""init

Revision ID: 66c53ffca33b
Revises: 
Create Date: 2025-05-31 10:43:19.048029

"""
from alembic import op
import sqlalchemy as sa
import sqlalchemy_utils  # 自动添加的依赖导入
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '66c53ffca33b'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('account_integrates',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('account_id', postgresql.UUID(), nullable=False),
    sa.Column('provider', sa.String(length=16), nullable=False),
    sa.Column('open_id', sa.String(length=255), nullable=False),
    sa.Column('encrypted_token', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='account_integrate_pkey'),
    sa.UniqueConstraint('account_id', 'provider', name='unique_account_provider'),
    sa.UniqueConstraint('provider', 'open_id', name='unique_provider_open_id')
    )
    op.create_table('account_permissions',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('account_id', postgresql.UUID(), nullable=False),
    sa.Column('resource_type', sa.String(length=32), nullable=False),
    sa.Column('resource_id', postgresql.UUID(), nullable=False),
    sa.Column('permisson_type', sa.String(length=32), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='account_permission_pkey'),
    sa.UniqueConstraint('resource_type', 'resource_id', 'account_id', name='unique_account_permission')
    )
    with op.batch_alter_table('account_permissions', schema=None) as batch_op:
        batch_op.create_index('index_account_resource', ['account_id', 'resource_type'], unique=False)

    op.create_table('accounts',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False, comment='用户名'),
    sa.Column('nickname', sa.String(length=255), nullable=True, comment='昵称'),
    sa.Column('email', sa.String(length=255), nullable=True, comment='邮箱'),
    sa.Column('email_verified_at', sa.DateTime(), nullable=True, comment='邮箱验证时间'),
    sa.Column('mobile', sa.String(length=255), nullable=True, comment='手机号'),
    sa.Column('password', sa.String(length=255), nullable=True, comment='密码'),
    sa.Column('password_salt', sa.String(length=255), nullable=True, comment='密码盐'),
    sa.Column('avatar', sa.String(length=255), nullable=True, comment='头像'),
    sa.Column('region', sa.String(length=255), nullable=True, comment='地区'),
    sa.Column('interface_language', sa.String(length=255), server_default=sa.text("'zh-cn'::character varying"), nullable=True, comment='界面语言'),
    sa.Column('interface_theme', sa.String(length=255), server_default=sa.text("'light'::character varying"), nullable=True, comment='界面主题'),
    sa.Column('timezone', sa.String(length=255), server_default=sa.text("'Asia/Shanghai'::character varying"), nullable=True, comment='时区'),
    sa.Column('last_login_at', sa.DateTime(), nullable=True, comment='最后登录时间'),
    sa.Column('last_login_ip', sa.String(length=255), nullable=True, comment='最后登录IP'),
    sa.Column('last_active_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False, comment='最后活跃时间'),
    sa.Column('status', sa.String(length=16), server_default=sa.text("'active'::character varying"), nullable=False, comment='状态'),
    sa.Column('initialized_at', sa.DateTime(), nullable=True, comment='初始化时间'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='account_pkey')
    )
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.create_index('account_email_idx', ['email'], unique=False)

    op.create_table('datasources',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, comment='主键'),
    sa.Column('name', sa.String(length=255), nullable=False, comment='数据源名称'),
    sa.Column('type', sa.String(length=50), nullable=False, comment='数据源类型'),
    sa.Column('host', sa.String(length=255), nullable=False, comment='数据源主机地址'),
    sa.Column('port', sa.Integer(), nullable=False, comment='数据源端口'),
    sa.Column('database', sa.String(length=255), nullable=False, comment='数据源数据库名称'),
    sa.Column('username', sa.String(length=255), nullable=False, comment='数据源用户名'),
    sa.Column('password', sqlalchemy_utils.types.encrypted.encrypted_type.EncryptedType(), nullable=True, comment='数据源密码加密字段'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('created_by', sa.String(length=50), server_default='system', nullable=False, comment='创建人'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('updated_by', sa.String(length=50), server_default='system', nullable=False, comment='更新人'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('http_request_logs',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False, comment='主键ID'),
    sa.Column('account_id', postgresql.UUID(), nullable=True, comment='用户账号ID'),
    sa.Column('ip_address', sa.String(length=255), nullable=False, comment='请求IP地址'),
    sa.Column('path', sa.String(length=255), nullable=False, comment='请求路径'),
    sa.Column('method', sa.String(length=10), nullable=False, comment='请求方法'),
    sa.Column('request_data', sa.Text(), nullable=True, comment='请求数据(包含请求参数和请求体)'),
    sa.Column('status_code', sa.Integer(), nullable=True, comment='响应状态码'),
    sa.Column('response_size', sa.Integer(), nullable=True, comment='响应内容大小(字节)'),
    sa.Column('response_error_data', sa.Text(), nullable=True, comment='响应错误内容'),
    sa.Column('user_agent', sa.String(length=512), nullable=True, comment='用户代理(浏览器/应用信息)'),
    sa.Column('os_info', sa.String(length=255), nullable=True, comment='操作系统信息'),
    sa.Column('referer', sa.String(length=512), nullable=True, comment='请求来源页面'),
    sa.Column('duration_ms', sa.Integer(), nullable=True, comment='请求处理时间(毫秒)'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id', name='http_request_log_pkey')
    )
    with op.batch_alter_table('http_request_logs', schema=None) as batch_op:
        batch_op.create_index('http_request_log_account_idx', ['account_id'], unique=False)
        batch_op.create_index('http_request_log_path_idx', ['path'], unique=False)

    op.create_table('invitation_codes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('batch', sa.String(length=255), nullable=False),
    sa.Column('code', sa.String(length=32), nullable=False),
    sa.Column('status', sa.String(length=16), server_default=sa.text("'unused'::character varying"), nullable=False),
    sa.Column('used_at', sa.DateTime(), nullable=True),
    sa.Column('used_by_account_id', postgresql.UUID(), nullable=True),
    sa.Column('deprecated_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='invitation_code_pkey')
    )
    with op.batch_alter_table('invitation_codes', schema=None) as batch_op:
        batch_op.create_index('invitation_codes_batch_idx', ['batch'], unique=False)
        batch_op.create_index('invitation_codes_code_idx', ['code', 'status'], unique=False)

    op.create_table('sys_departments',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='部门名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='部门编码'),
    sa.Column('leader', sa.String(length=50), nullable=True, comment='负责人'),
    sa.Column('phone', sa.String(length=20), nullable=True, comment='联系电话'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='邮箱'),
    sa.Column('sort', sa.Integer(), nullable=False, comment='排序'),
    sa.Column('parent_id', postgresql.UUID(), nullable=True, comment='父级ID'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='状态：active-启用，inactive-禁用'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['parent_id'], ['sys_departments.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('sys_menus',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='菜单名称'),
    sa.Column('path', sa.String(length=200), nullable=False, comment='路由路径'),
    sa.Column('icon', sa.String(length=50), nullable=True, comment='图标'),
    sa.Column('sort', sa.Integer(), nullable=False, comment='排序'),
    sa.Column('type', sa.String(length=20), nullable=False, comment='类型：menu-菜单，button-按钮'),
    sa.Column('permission', sa.String(length=50), nullable=True, comment='权限标识'),
    sa.Column('parent_id', postgresql.UUID(), nullable=True, comment='父级ID'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='状态：active-启用，inactive-禁用'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['parent_id'], ['sys_menus.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('sys_permissions',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='权限名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='权限标识'),
    sa.Column('type', sa.String(length=20), nullable=False, comment='类型：menu-菜单，button-按钮'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='状态：active-启用，inactive-禁用'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('sys_roles',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='角色名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='角色编码'),
    sa.Column('description', sa.String(length=200), nullable=True, comment='描述'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='状态：active-启用，inactive-禁用'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('data_source_app',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False, comment='主键'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='应用名称'),
    sa.Column('alias', sa.String(length=50), nullable=False, comment='应用别名'),
    sa.Column('description', sa.Text(), nullable=True, comment='应用描述'),
    sa.Column('datasource_id', postgresql.UUID(), nullable=False, comment='数据源ID'),
    sa.Column('datasource_type', sa.String(length=50), server_default='mysql', nullable=False, comment='数据源类型'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('created_by', sa.String(length=50), server_default='system', nullable=False, comment='创建人'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.Column('updated_by', sa.String(length=50), server_default='system', nullable=False, comment='更新人'),
    sa.Column('initial_prompt', sa.Text(), nullable=True, comment='初始提示词'),
    sa.Column('app_key', sa.String(length=50), server_default=sa.text('uuid_generate_v4()'), nullable=True, comment='应用密钥'),
    sa.Column('status', sa.String(length=50), server_default='active', nullable=False, comment='状态'),
    sa.ForeignKeyConstraint(['datasource_id'], ['datasources.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('sys_role_permissions',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('role_id', postgresql.UUID(), nullable=False),
    sa.Column('permission_id', postgresql.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.ForeignKeyConstraint(['permission_id'], ['sys_permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['sys_roles.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('sys_users',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名'),
    sa.Column('nickname', sa.String(length=50), nullable=False, comment='昵称'),
    sa.Column('email', sa.String(length=100), nullable=False, comment='邮箱'),
    sa.Column('phone', sa.String(length=20), nullable=True, comment='手机号'),
    sa.Column('department_id', postgresql.UUID(), nullable=True, comment='部门ID'),
    sa.Column('role_id', postgresql.UUID(), nullable=True, comment='角色ID'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='状态：active-启用，inactive-禁用'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['department_id'], ['sys_departments.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['sys_roles.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('phone'),
    sa.UniqueConstraint('username')
    )
    op.create_table('chatdb_message',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False, comment='主键'),
    sa.Column('app_id', postgresql.UUID(), nullable=False, comment='应用ID'),
    sa.Column('question', sa.String(length=1024), nullable=False, comment='用户问题'),
    sa.Column('answer_text', sa.Text(), nullable=True, comment='回复消息'),
    sa.Column('answer_sql', sa.Text(), nullable=True, comment='回复SQL语句'),
    sa.Column('answer_sql_valid', sa.Boolean(), nullable=False, comment='SQL语句是否有效'),
    sa.Column('answer_data', sa.Text(), nullable=True, comment='回复数据'),
    sa.Column('answer_data_row_count', sa.Integer(), server_default=sa.text('0'), nullable=False, comment='回复数据行数'),
    sa.Column('answer_charts', sa.Text(), nullable=True, comment='回复图表代码'),
    sa.Column('from_source', sa.String(length=16), nullable=False, comment='来源'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('created_by', sa.String(length=50), nullable=True, comment='创建人'),
    sa.Column('is_recommend', sa.Boolean(), server_default=sa.text('false'), nullable=False, comment='是否推荐'),
    sa.Column('recommend_at', sa.DateTime(), nullable=True, comment='推荐时间'),
    sa.Column('recommend_by', sa.String(length=50), nullable=True, comment='推荐人'),
    sa.ForeignKeyConstraint(['app_id'], ['data_source_app.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('data_source_app_statistics',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False, comment='主键'),
    sa.Column('app_id', postgresql.UUID(), nullable=False, comment='应用ID'),
    sa.Column('table_count', sa.Integer(), server_default=sa.text('0'), nullable=False, comment='表数'),
    sa.Column('training_data_count', sa.Integer(), server_default=sa.text('0'), nullable=False, comment='训练数据量'),
    sa.Column('message_count', sa.Integer(), server_default=sa.text('0'), nullable=False, comment='消息数'),
    sa.Column('success_message_count', sa.Integer(), server_default=sa.text('0'), nullable=False, comment='成功消息数'),
    sa.ForeignKeyConstraint(['app_id'], ['data_source_app.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('data_source_app_statistics', schema=None) as batch_op:
        batch_op.create_index('ix_data_source_app_statistics_app_id', ['app_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('data_source_app_statistics', schema=None) as batch_op:
        batch_op.drop_index('ix_data_source_app_statistics_app_id')

    op.drop_table('data_source_app_statistics')
    op.drop_table('chatdb_message')
    op.drop_table('sys_users')
    op.drop_table('sys_role_permissions')
    op.drop_table('data_source_app')
    op.drop_table('sys_roles')
    op.drop_table('sys_permissions')
    op.drop_table('sys_menus')
    op.drop_table('sys_departments')
    with op.batch_alter_table('invitation_codes', schema=None) as batch_op:
        batch_op.drop_index('invitation_codes_code_idx')
        batch_op.drop_index('invitation_codes_batch_idx')

    op.drop_table('invitation_codes')
    with op.batch_alter_table('http_request_logs', schema=None) as batch_op:
        batch_op.drop_index('http_request_log_path_idx')
        batch_op.drop_index('http_request_log_account_idx')

    op.drop_table('http_request_logs')
    op.drop_table('datasources')
    with op.batch_alter_table('accounts', schema=None) as batch_op:
        batch_op.drop_index('account_email_idx')

    op.drop_table('accounts')
    with op.batch_alter_table('account_permissions', schema=None) as batch_op:
        batch_op.drop_index('index_account_resource')

    op.drop_table('account_permissions')
    op.drop_table('account_integrates')
    # ### end Alembic commands ###
