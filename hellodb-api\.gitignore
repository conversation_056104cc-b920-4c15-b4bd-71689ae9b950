# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# history插件
.history  
/.history

# 项目数据文件
/data

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*.pyo
*.pyd
# PEP 582; used by e.g. github.com/<PERSON>/pyflow
__pypackages__/
6b296131-f4b2-4a89-b258-c55e572a270f
586a5921-67a3-4248-9545-1546a3192f83
b52dc8ef-0d37-43ab-a605-56b710bcd84b

