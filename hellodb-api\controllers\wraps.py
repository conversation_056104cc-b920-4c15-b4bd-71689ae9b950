import functools
import logging
from flask import request
from flask_restful import Resource
from controllers.api_response import ApiResponse

from werkzeug.exceptions import NotFound, Unauthorized

from libs.passport import PassportService
from services.account_service import AccountService
from services.license_service import LicenseService

# 导出Resource类，这样其他文件可以从controllers.wraps导入Resource
__all__ = ['WebApiResource', 'license_required']

def validate_jwt_token(view=None):
    def decorator(view):
        @functools.wraps(view)
        def decorated(*args, **kwargs):
            
            ### 调试 测试期间，绕过
            end_user = decode_jwt_token()
            # return view(end_user, *args, **kwargs)
            
            return view(*args, **kwargs)

            
        return decorated
    if view:
        return decorator(view)
    return decorator

def decode_jwt_token():
    
    auth_header = request.headers.get('Authorization')
    if auth_header is None:
        raise Unauthorized('Authorization header is missing.')

    if ' ' not in auth_header:
        raise Unauthorized('Invalid Authorization header format. Expected \'Bearer <api-key>\' format.')
    
    auth_scheme, tk = auth_header.split(None, 1)
    auth_scheme = auth_scheme.lower()

    if auth_scheme != 'bearer':
        raise Unauthorized('Invalid Authorization header format. Expected \'Bearer <api-key>\' format.')
    decoded = PassportService().verify(tk)
    #app_code = decoded.get('app_code')
    #app_model = db.session.query(App).filter(App.id == decoded['app_id']).first()
    #site = db.session.query(Site).filter(Site.code == app_code).first()
    #if not app_model:
    #    raise NotFound()
    #if not app_code or not site:
    #    raise Unauthorized('Site URL is no longer valid.')
    #if app_model.enable_site is False:
    #    raise Unauthorized('Site is disabled.')
    # end_user = db.session.query(EndUser).filter(EndUser.id == decoded['end_user_id']).first()

    account = AccountService.load_account(decoded['user_id'])
    if not account:
        raise NotFound()

    return account

class WebApiResource(Resource):
    # method_decorators = [validate_jwt_token]的含义是 在调用视图函数之前，先调用validate_jwt_token装饰器
    method_decorators = [validate_jwt_token]


def license_required(f):
    """
    License验证装饰器
    支持反向代理场景下的域名验证
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        """
        首先验证license有效，再验证域名是否匹配
        """

        if not LicenseService.verify_license():
            return ApiResponse.error(msg=f"License校验失败，请联系您的技术支持人员！")
        
        if not LicenseService.is_domain_valid(request):
            return ApiResponse.error(msg="License校验失败：域名不匹配")

        # 验证通过，继续执行原函数
        return f(*args, **kwargs)
            

    return decorated_function