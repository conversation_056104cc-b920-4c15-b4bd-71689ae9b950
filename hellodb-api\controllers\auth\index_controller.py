import logging
from urllib.parse import urljoin, urlparse
import random
import qrcode
import io
import base64

from flask import Response, abort, json, jsonify, request, send_file
import flask
from flask_login import login_user, logout_user
from flask_restful import fields, marshal_with, reqparse, current_app
from flask_restful.inputs import int_range
from werkzeug.exceptions import InternalServerError, NotFound

from controllers.auth import api
import services
from controllers.wraps import Resource
from services.account_service import AccountService
from extensions.ext_login import login_manager
from services.sms_service import SmsService
from services.redis_service import RedisService

import random  # 添加随机数生成库
import qrcode  # 用于生成二维码

def is_safe_url(target):
    if not target:
        return True
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and ref_url.netloc == test_url.netloc


@login_manager.unauthorized_handler
def unauthorized_handler():
    """Handle unauthorized requests."""
    return Response(json.dumps({
        'code': '401',
        'msg': "Unauthorized."
    }), status=401, content_type="application/json")

class LoginApi(Resource):

    def post(self):
        # 这里我们使用一个类，来表示和验证客户端表单数据
        # 例如，WTForms 库可以用来为我们处理这些工作，
        # 我们使用自定义的 LoginForm 来验证表单数据。
        parser = reqparse.RequestParser()  
        parser.add_argument('username', type=str, required=True, help='Username is required')  
        parser.add_argument('password', type=str, required=True, help='Password is required')

        args = parser.parse_args()
        username = args['username']  
        password = args['password']

        # 如果username包含@，则识别为邮箱
        email = username if '@' in username else None

        # 如果email为空，则登录失败，提示用户email不能为空
        if email is None:
            return {"code": 401, "msg": "登陆失败，email不能为空"}

        account = AccountService.authenticate(email, password)
  
        if account:
            login_user(account)

            token = AccountService.get_account_jwt_token(account)

            flask.flash('Logged in successfully.')
            next = flask.request.args.get('next')
            # is_safe_url 用来检查url是否可以安全的重定向。
            # 有关示例，参见 http://flask.pocoo.org/snippets/62/ 。
            if not is_safe_url(next):
                return flask.abort(400)

            # next = next or 'flask.url_for('index')'
            next = next or ''
            return  {"code": 200, "msg": "登陆成功", "data": {"token": token}}
        
        return {"code": 401, "msg": "登陆失败，无效的登录请求"}


class VerifyCodeApi(Resource):
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('mobile', type=str, required=True, help='手机号不能为空')
        args = parser.parse_args()
        
        mobile = args['mobile']
        
        # 生成6位随机验证码
        verify_code = str(random.randint(100000, 999999))
        
        try:
            
            # 如果是调试模式启动，不发送验证码，而是直接返回验证码
            if current_app.config['DEBUG']:
                RedisService.set(f"verify_code:{mobile}", verify_code, ex=300)
                print(f"验证码发送成功,{verify_code}")
                return {"code": 500, "msg": f"验证码发送成功,{verify_code}"}
            
            # 发送短信验证码
            result = SmsService.send_verify_code(mobile, verify_code)
            
            if result:
                # 将验证码存入Redis，设置5分钟过期
                RedisService.set(f"verify_code:{mobile}", verify_code, ex=300)
                return {"code": 200, "msg": "验证码发送成功"}
            else:
                return {"code": 500, "msg": "验证码发送失败"}
        except Exception as e:
            logging.error(f"发送验证码失败: {str(e)}")
            return {"code": 500, "msg": "验证码发送失败"}
        

class LoginByMobile(Resource):

    def verify_code(self, mobile, code):
        verify_code = RedisService.get(f"verify_code:{mobile}")
        return verify_code == code
        # return code == '654123'

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('mobile', type=str, required=True, help='手机号不能为空')
        parser.add_argument('code', type=str, required=True, help='验证码不能为空')
        args = parser.parse_args()
        mobile = args['mobile']
        code = args['code']
        if self.verify_code(mobile, code):
            account = AccountService.load_account_by_mobile(mobile)
            if account:
                login_user(account)
                token = AccountService.get_account_jwt_token(account)
                AccountService.update_last_login(account, request)
                return {"code": 200, "msg": "登陆成功", "data": {"token": token}}
            else:
                # 账号不存在，创建账号后登录
                account = AccountService.create_account(name = mobile, mobile = mobile)
                token  = AccountService.get_account_jwt_token(account)
                AccountService.update_last_login(account, request)
                return {"code": 200, "msg": "登陆成功", "data": {"token": token}}
        else:
            return {"code": 401, "msg": "登陆失败，验证码错误"}
            

class WechatQrcodeApi(Resource):
    def get(self):
        try:
            # 生成随机场景值
            scene_id = random.randint(100000, 999999)
            
            # 生成二维码内容
            qr_content = f"https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=MzA4MTA2Nzg0Mw==#wechat_redirect&scene_id={scene_id}"
            
            # 创建二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_content)
            qr.make(fit=True)
            
            # 生成二维码图片
            img = qr.make_image(fill_color="black", back_color="white")
            
            # 将图片转换为base64
            buffered = io.BytesIO()
            img.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()
            
            # 将场景值存入Redis，用于后续验证
            RedisService.set(f"wechat_scene:{scene_id}", "pending", ex=300)
            
            return {
                "code": 200,
                "msg": "二维码生成成功",
                "data": {
                    "qrcode": img_str,
                    "scene_id": scene_id
                }
            }
        except Exception as e:
            logging.error(f"生成二维码失败: {str(e)}")
            return {"code": 500, "msg": "二维码生成失败"}
        


class LogoutApi(Resource):

    def get(self):
        logout_user()
        return {"code": 200, "msg": "登出成功"}


api.add_resource(LoginApi, '/auth/login')
api.add_resource(LoginByMobile, '/auth/login-by-mobile') 
api.add_resource(VerifyCodeApi, '/auth/get-verify-code')
api.add_resource(WechatQrcodeApi, '/auth/wechat/qrcode')
api.add_resource(LogoutApi, '/auth/logout')