import redis
from flask import current_app
import logging

class RedisService:
    _redis_client = None

    @classmethod
    def get_client(cls):
        if cls._redis_client is None:
            try:
                cls._redis_client = current_app.extensions['redis']
            except Exception as e:
                logging.error(f"Redis连接失败: {str(e)}")
                raise
        return cls._redis_client

    @classmethod
    def set(cls, key, value, ex=None):
        """
        设置键值对
        :param key: 键
        :param value: 值
        :param ex: 过期时间（秒）
        :return: bool
        """
        try:
            client = cls.get_client()
            return client.set(key, value, ex=ex)
        except Exception as e:
            logging.error(f"Redis设置失败: {str(e)}")
            return False

    @classmethod
    def get(cls, key):
        """
        获取键对应的值
        :param key: 键
        :return: 值
        """
        try:
            client = cls.get_client()
            return client.get(key)
        except Exception as e:
            logging.error(f"Redis获取失败: {str(e)}")
            return None

    @classmethod
    def delete(cls, key):
        """
        删除键
        :param key: 键
        :return: bool
        """
        try:
            client = cls.get_client()
            return client.delete(key)
        except Exception as e:
            logging.error(f"Redis删除失败: {str(e)}")
            return False 