from flask_restful import fields
from datetime import datetime

class CustomDateTime(fields.Raw):
    """
    自定义日期时间字段，支持自定义格式
    默认格式：'%Y-%m-%d %H:%M:%S'
    """
    def __init__(self, dt_format='%Y-%m-%d %H:%M:%S', **kwargs):
        super(CustomDateTime, self).__init__(**kwargs)
        self.dt_format = dt_format

    def format(self, value):
        if value is None:
            return None
        if isinstance(value, datetime):
            return value.strftime(self.dt_format)
        return value 