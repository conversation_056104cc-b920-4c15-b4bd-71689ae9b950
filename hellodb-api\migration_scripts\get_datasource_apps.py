#!/usr/bin/env python3
"""
获取所有需要迁移的DataSourceApp列表
"""
import os
import sys
import json
from datetime import datetime

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.datasource import DataSourceApp
from app import create_app

def get_all_datasource_apps():
    """获取所有活跃的DataSourceApp"""
    app = create_app()
    
    with app.app_context():
        # 查询所有活跃的DataSourceApp
        apps = DataSourceApp.query.filter_by(status='active').all()
        
        app_list = []
        for app_item in apps:
            app_info = {
                'id': str(app_item.id),
                'name': app_item.name,
                'alias': app_item.alias,
                'datasource_type': app_item.datasource_type,
                'created_by': app_item.created_by,
                'created_at': app_item.created_at.strftime('%Y-%m-%d %H:%M:%S') if app_item.created_at else None,
                'old_vector_path': f'/data/chroma/{app_item.alias}',
                'new_vector_path': f'/data/hellodb/chroma/{app_item.alias}'
            }
            app_list.append(app_info)
        
        return app_list

def save_migration_list(app_list):
    """保存迁移列表到文件"""
    migration_data = {
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_apps': len(app_list),
        'apps': app_list
    }
    
    # 创建输出目录
    output_dir = 'migration_scripts/output'
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存到JSON文件
    output_file = f'{output_dir}/datasource_apps_migration_list.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(migration_data, f, ensure_ascii=False, indent=2)
    
    print(f"迁移列表已保存到: {output_file}")
    return output_file

def print_summary(app_list):
    """打印摘要信息"""
    print(f"\n📊 DataSourceApp迁移摘要:")
    print(f"总数量: {len(app_list)}")
    
    # 按数据源类型分组
    type_count = {}
    for app in app_list:
        db_type = app['datasource_type']
        type_count[db_type] = type_count.get(db_type, 0) + 1
    
    print(f"\n按数据源类型分布:")
    for db_type, count in type_count.items():
        print(f"  {db_type}: {count}")
    
    print(f"\n前10个应用:")
    for i, app in enumerate(app_list[:10]):
        print(f"  {i+1}. {app['name']} ({app['alias']}) - {app['datasource_type']}")
    
    if len(app_list) > 10:
        print(f"  ... 还有 {len(app_list) - 10} 个应用")

if __name__ == "__main__":
    try:
        print("🔍 正在获取所有DataSourceApp...")
        app_list = get_all_datasource_apps()
        
        print_summary(app_list)
        
        output_file = save_migration_list(app_list)
        
        print(f"\n✅ 成功获取 {len(app_list)} 个DataSourceApp")
        print(f"📄 详细信息已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 获取DataSourceApp失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
