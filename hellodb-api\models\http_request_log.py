from extensions.ext_database import db
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

class HttpRequestLog(db.Model):
    __tablename__ = 'http_request_logs'
    __table_args__ = (
        db.PrimaryKeyConstraint('id', name='http_request_log_pkey'),
        db.Index('http_request_log_path_idx', 'path'),
        db.Index('http_request_log_account_idx', 'account_id'),
    )

    id = db.Column(UUID, server_default=db.text('uuid_generate_v4()'), comment='主键ID')
    account_id = db.Column(UUID, nullable=True, comment='用户账号ID')
    ip_address = db.Column(db.String(255), nullable=False, comment='请求IP地址')
    path = db.Column(db.String(255), nullable=False, comment='请求路径')
    method = db.Column(db.String(10), nullable=False, comment='请求方法')
    request_data = db.Column(db.Text, nullable=True, comment='请求数据(包含请求参数和请求体)')
    status_code = db.Column(db.Integer, nullable=True, comment='响应状态码')
    response_size = db.Column(db.Integer, nullable=True, comment='响应内容大小(字节)')
    response_error_data = db.Column(db.Text, nullable=True, comment='响应错误内容')
    user_agent = db.Column(db.String(512), nullable=True, comment='用户代理(浏览器/应用信息)')
    os_info = db.Column(db.String(255), nullable=True, comment='操作系统信息')
    referer = db.Column(db.String(512), nullable=True, comment='请求来源页面')
    duration_ms = db.Column(db.Integer, nullable=True, comment='请求处理时间(毫秒)')
    created_at = db.Column(db.DateTime(timezone=True), nullable=False, server_default=func.now(), comment='创建时间')

    def to_dict(self):
        return {
            'id': self.id,
            'account_id': self.account_id,
            'ip_address': self.ip_address,
            'path': self.path,
            'method': self.method,
            'request_data': self.request_data,
            'status_code': self.status_code,
            'response_size': self.response_size,
            'response_error_data': self.response_error_data,
            'user_agent': self.user_agent,
            'os_info': self.os_info,
            'referer': self.referer,
            'duration_ms': self.duration_ms,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
        }

