"""empty message

Revision ID: 5177c4b2fec0
Revises: 05da4b38be21
Create Date: 2025-06-17 10:45:52.450824

"""
from alembic import op
import sqlalchemy as sa
import sqlalchemy_utils  # 自动添加的依赖导入


# revision identifiers, used by Alembic.
revision = '5177c4b2fec0'
down_revision = '05da4b38be21'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasources', schema=None) as batch_op:
        batch_op.add_column(sa.Column('tds_version', sa.String(length=50), nullable=True, comment='数据源TDS版本'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasources', schema=None) as batch_op:
        batch_op.drop_column('tds_version')

    # ### end Alembic commands ###
