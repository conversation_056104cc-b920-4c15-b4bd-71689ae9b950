'''
封装 API 响应
'''
class ApiResponse:
    @staticmethod
    def success(data=None, msg="操作成功"):
        return {"code": 200, "msg": msg, "data": data}

    @staticmethod
    def error(code=500, msg="服务器内部错误"):
        return {"code": code, "msg": msg}
    
    @staticmethod
    def _paginated(items, page, per_page, total_pages, total_items, msg="查询成功"):
        return ApiResponse.success(
            data={
                "items": items,
                "page": page,
                "per_page": per_page,
                "total_pages": total_pages,
                "total": total_items
            },
            msg=msg
        )
    
    @staticmethod
    def paginated_from_pagination(pagination, items_serializer=lambda x: [item.to_dict() for item in x], msg="查询成功"):
        """
        直接从 SQLAlchemy pagination 对象包装分页响应。
        :param pagination: SQLAlchemy 的 pagination 对象
        :param items_serializer: 序列化 items 的函数，默认为调用 to_dict
        :param msg: 响应消息
        """
        items = items_serializer(pagination.items)
        return ApiResponse._paginated(
            items=items,
            page=pagination.page,
            per_page=pagination.per_page,
            total_pages=pagination.pages,
            total_items=pagination.total,
            msg=msg
        )