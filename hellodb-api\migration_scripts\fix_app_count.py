#!/usr/bin/env python3
"""
修复应用数量问题的脚本
"""
import os
import sys

def update_migration_script_for_all_apps():
    """更新迁移脚本以包含所有状态的应用"""
    
    script_path = 'migration_scripts/full_migration.py'
    
    # 读取原文件
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换SQL查询，移除状态限制
    old_query = """SELECT DISTINCT alias 
                FROM data_source_app 
                WHERE status = 'active' 
                ORDER BY alias"""
    
    new_query = """SELECT DISTINCT alias 
                FROM data_source_app 
                ORDER BY alias"""
    
    if old_query in content:
        content = content.replace(old_query, new_query)
        
        # 写回文件
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新 {script_path}")
        print("   现在将迁移所有状态的应用")
        return True
    else:
        print(f"❌ 未找到需要替换的查询语句")
        return False

def restore_migration_script_for_active_only():
    """恢复迁移脚本为仅迁移活跃应用"""
    
    script_path = 'migration_scripts/full_migration.py'
    
    # 读取原文件
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换SQL查询，添加状态限制
    old_query = """SELECT DISTINCT alias 
                FROM data_source_app 
                ORDER BY alias"""
    
    new_query = """SELECT DISTINCT alias 
                FROM data_source_app 
                WHERE status = 'active' 
                ORDER BY alias"""
    
    if old_query in content and "WHERE status = 'active'" not in content:
        content = content.replace(old_query, new_query)
        
        # 写回文件
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已恢复 {script_path}")
        print("   现在将仅迁移活跃状态的应用")
        return True
    else:
        print(f"❌ 未找到需要替换的查询语句或已经是活跃状态限制")
        return False

def main():
    """主函数"""
    print("🔧 修复应用数量问题")
    print("=" * 50)
    
    print("当前问题分析:")
    print("1. 您提到总共有82个应用")
    print("2. 迁移工具只处理了45个应用")
    print("3. 可能的原因:")
    print("   - 数据库中只有45个 status='active' 的应用")
    print("   - 其余37个应用可能是其他状态(inactive, pending等)")
    print()
    
    print("解决方案选择:")
    print("1. 迁移所有状态的应用(包括非活跃应用)")
    print("2. 仅迁移活跃状态的应用(当前设置)")
    print("3. 先诊断应用状态分布")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        print("\n🔄 更新迁移脚本以包含所有状态的应用...")
        if update_migration_script_for_all_apps():
            print("\n✅ 更新完成！")
            print("现在可以运行以下命令进行全量迁移:")
            print("python migration_scripts/full_migration.py")
            print()
            print("⚠️  注意: 这将迁移所有状态的应用，包括可能已停用的应用")
        
    elif choice == '2':
        print("\n🔄 恢复迁移脚本为仅迁移活跃应用...")
        if restore_migration_script_for_active_only():
            print("\n✅ 恢复完成！")
            print("现在将仅迁移活跃状态的应用")
        
    elif choice == '3':
        print("\n🔍 运行诊断脚本...")
        os.system("python migration_scripts/diagnose_apps.py")
        
    elif choice == '4':
        print("退出")
        return
    
    else:
        print("❌ 无效选择")
        return
    
    print("\n📋 后续建议:")
    print("1. 运行诊断脚本了解详细情况: python migration_scripts/diagnose_apps.py")
    print("2. 如果确认要迁移所有应用，选择选项1后重新运行迁移")
    print("3. 如果只需要迁移活跃应用，当前的45个应用可能就是正确的数量")

if __name__ == "__main__":
    main()
