from models.system import User
from extensions.ext_database import db

class UserService:
    @staticmethod
    def create_user(data):
        """创建用户"""
        user = User(
            username=data['username'],
            nickname=data['nickname'],
            email=data['email'],
            phone=data.get('phone'),
            department_id=data.get('department_id'),
            role_id=data.get('role_id'),
            status=data.get('status', 'active')
        )
        
        db.session.add(user)
        db.session.commit()
        return user

    @staticmethod
    def update_user(user, data):
        """更新用户信息"""
        for key, value in data.items():
            if hasattr(user, key):
                setattr(user, key, value)
        db.session.commit()

    @staticmethod
    def delete_user(user):
        """软删除用户"""
        user.status = 'deleted'
        db.session.commit()

    @staticmethod
    def get_user_by_id(user_id):
        """根据ID获取用户"""
        return User.query.get(user_id)
