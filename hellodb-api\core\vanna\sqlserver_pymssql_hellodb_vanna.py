import pandas as pd
from typing import Optional

from core.vanna.hellodb_vanna import HellodbVanna
from vanna.exceptions import DependencyError, ImproperlyConfigured
from models.datasource import Datasource


class SQLServerPyMSSQLHellodbVanna(HellodbVanna):
    """
    Microsoft SQL Server implementation of HellodbVanna.
    """
    
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a SQL Server database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            import pymssql
        except ImportError:
            raise DependencyError(
                "You need to install required dependencies to execute this method,"
                " run command: \npip install pymssql"
            )

        host = datasource.host
        if not host:
            raise ImproperlyConfigured("Please set your SQL Server host")

        port = datasource.port
        if not port:
            raise ImproperlyConfigured("Please set your SQL Server port")

        dbname = datasource.database
        if not dbname:
            raise ImproperlyConfigured("Please set your SQL Server database")

        username = datasource.username
        if not username:
            raise ImproperlyConfigured("Please set your SQL Server user")

        password = datasource.password
        if not password:
            raise ImproperlyConfigured("Please set your SQL Server password")
        
        tds_version = datasource.tds_version
        if not tds_version:
            tds_version = 7.2
        
        conn = pymssql.connect(host=host, user=username, password=password, database=dbname, port=port, tds_version=tds_version, as_dict=True)

        def run_sql_mssql(sql: str):
            with conn.cursor() as cursor:
                cursor.execute(sql)
                return pd.DataFrame(cursor.fetchall())
            
        
        self.dialect = "T-SQL / Microsoft SQL Server"
        self.run_sql_is_set = True
        self.run_sql = run_sql_mssql
        
    
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected MSSQL database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        
        if database_name is None:
            # Get the current database name
            df_current_db = self.run_sql("SELECT DB_NAME() as db_name")
            database_name = df_current_db.iloc[0]['db_name']
        
        # Get all columns from the information schema
        query = f"""
        SELECT 
            '{database_name}' as table_catalog,
            TABLE_SCHEMA as table_schema, 
            TABLE_NAME as table_name, 
            COLUMN_NAME as column_name, 
            DATA_TYPE as data_type,
            ep.value as column_comment
        FROM 
            {database_name}.INFORMATION_SCHEMA.COLUMNS c
        LEFT JOIN 
            sys.extended_properties ep ON 
            ep.major_id = OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME) AND
            ep.minor_id = c.ORDINAL_POSITION AND
            ep.name = 'MS_Description'
        ORDER BY 
            TABLE_SCHEMA, TABLE_NAME, ORDINAL_POSITION
        """
        
        return self.run_sql(query)
