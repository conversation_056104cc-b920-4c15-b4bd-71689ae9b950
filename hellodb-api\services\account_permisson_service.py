from datetime import datetime

from extensions.ext_database import db

from models.account import Account, AccountPermission
from services.account_service import AccountService
from services.errors.account import (
    AccountNotFound,
)


class AccountPermissonService:

    @staticmethod
    def save_account_permission(account_id: str, resource_type: str, resource_id: str, permisson_type: str) -> None:
        '''
        保存用户权限，如果已经存在记录则更新，如果不存在则新增
        '''
        item = AccountPermission.query.filter_by(
            account_id=account_id, resource_type=resource_type, resource_id=resource_id).first()
        if item:
            item.permisson_type = permisson_type
            item.updated_at = datetime.now()
            db.session.commit()
        else:
            item = AccountPermission(
                account_id=account_id,
                resource_type=resource_type,
                resource_id=resource_id,
                permisson_type=permisson_type
            )
            db.session.add(item)
            db.session.commit()

    @staticmethod
    def save_account_permisson_through_username(username: str, resource_type: str, resource_id: str, permisson_type: str) -> None:
        '''
        通过用户名保存用户权限，如果已经存在记录则更新，如果不存在则新增
        '''
        account = AccountService.load_account_by_username(username)
        if not account:
            raise AccountNotFound("要添加的用户不存在")
        else:
            AccountPermissonService.save_account_permission(
                account.id, resource_type, resource_id, permisson_type)

    @staticmethod
    def save_account_permisson_batch(account_ids: list[str], resource_type: str, resource_id: str, permisson_type: str) -> None:
        '''
        批量保存用户权限，首先情况该资源对应的权限用户，如果已经存在记录则更新，如果不存在则新增
        '''
        AccountPermissonService.clear_resource_permissions(resource_type, resource_id, permisson_type)
        for account_id in account_ids:
            AccountPermissonService.save_account_permission(
                account_id, resource_type, resource_id, permisson_type)

    @staticmethod
    def save_account_permisson_batch_through_username(usernames: list[str], resource_type: str, resource_id: str, permisson_type: str) -> None:
        '''
        通过用户名批量保存用户权限，首先情况该资源对应的权限用户，如果已经存在记录则更新，如果不存在则新增
        '''
        AccountPermissonService.clear_resource_permissions(resource_type, resource_id, permisson_type)
        for username in usernames:
            AccountPermissonService.save_account_permisson_through_username(
                username, resource_type, resource_id, permisson_type)
            
            
    @staticmethod
    def delete_account_permission(account_id: str, resource_type: str, resource_id: str) -> None:
        '''
        删除用户权限
        '''
        item = AccountPermission.query.filter_by(
            account_id=account_id, resource_type=resource_type, resource_id=resource_id).first()
        if item:
            db.session.delete(item)
            db.session.commit()
            return True
        
        return False
    
    
    @staticmethod
    def clear_resource_permissions(resource_type: str, resource_id: str, permisson_type: str) -> None:
        '''
        删除某资源下的某类权限
        '''
        items = AccountPermission.query.filter_by(
            resource_type=resource_type, resource_id=resource_id, permisson_type=permisson_type).all()
        for item in items:
            db.session.delete(item)
            db.session.commit()
            return True
        
        return False


    @staticmethod
    def get_account_all_resources(account_id: str, resource_type: str) -> list[AccountPermission]:
        '''
        获取用户针对某类型的所有资源
        '''
        return AccountPermission.query.filter_by(account_id=account_id, resource_type=resource_type).all()

    @staticmethod
    def get_resource_all_accounts(resource_type: str, resource_id: str) -> list[str]:

        # 关联account表查询出所有的account昵称，AccountPermission表中只存了account_id，filter多个条件
        result = db.session.query(Account.name).join(AccountPermission, Account.id == AccountPermission.account_id).filter(
            AccountPermission.resource_id == resource_id, AccountPermission.resource_type == resource_type).all()
        
        account_names = [name[0] for name in result]  # 提取出结果中的名称

        return account_names
    
    @staticmethod
    def check_permission(account_id: str, resource_id: str, resource_type: str = None, permisson_type: str = None) -> bool:
        # 开始构建查询
        query = AccountPermission.query.filter_by(account_id=account_id, resource_id=resource_id)

        # 仅在 resource_type 不为 None 时添加过滤条件
        if resource_type is not None:
            query = query.filter_by(resource_type=resource_type)

        # 仅在 permisson_type 不为 None 时添加过滤条件
        if permisson_type is not None:
            query = query.filter_by(permisson_type=permisson_type)

        # 执行查询并返回结果
        item = query.first()
        return item is not None  # 返回是否找到记录


