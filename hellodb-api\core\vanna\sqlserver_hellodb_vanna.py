import pandas as pd
from typing import Optional

from core.vanna.hellodb_vanna import HellodbVanna
from vanna.exceptions import DependencyError, ImproperlyConfigured
from models.datasource import Datasource


class SQLServerHellodbVanna(HellodbVanna):
    """
    Microsoft SQL Server implementation of HellodbVanna.
    """
    
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a SQL Server database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            import pyodbc
        except ImportError:
            raise DependencyError(
                "You need to install required dependencies to execute this method,"
                " run command: \npip install pyodbc"
            )

        host = datasource.host
        if not host:
            raise ImproperlyConfigured("Please set your SQL Server host")

        port = datasource.port
        if not port:
            raise ImproperlyConfigured("Please set your SQL Server port")

        dbname = datasource.database
        if not dbname:
            raise ImproperlyConfigured("Please set your SQL Server database")

        username = datasource.username
        if not username:
            raise ImproperlyConfigured("Please set your SQL Server user")

        password = datasource.password
        if not password:
            raise ImproperlyConfigured("Please set your SQL Server password")

        # 拼接odbc_conn_str
        odbc_conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={host},{port};DATABASE={dbname};UID={username};PWD={password}"
        
        # 如果启用了SSL，添加加密配置
        if datasource.ssl:
            odbc_conn_str += ";Encrypt=yes;TrustServerCertificate=no"
        else:
            odbc_conn_str += ";Encrypt=no;"
            
        return self.connect_to_mssql(odbc_conn_str)
    
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected MSSQL database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        
        if database_name is None:
            # Get the current database name
            df_current_db = self.run_sql("SELECT DB_NAME() as db_name")
            database_name = df_current_db.iloc[0]['db_name']
        
        # Get all columns from the information schema
        query = f"""
        SELECT 
            '{database_name}' as table_catalog,
            TABLE_SCHEMA as table_schema, 
            TABLE_NAME as table_name, 
            COLUMN_NAME as column_name, 
            DATA_TYPE as data_type,
            ep.value as column_comment
        FROM 
            {database_name}.INFORMATION_SCHEMA.COLUMNS c
        LEFT JOIN 
            sys.extended_properties ep ON 
            ep.major_id = OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME) AND
            ep.minor_id = c.ORDINAL_POSITION AND
            ep.name = 'MS_Description'
        ORDER BY 
            TABLE_SCHEMA, TABLE_NAME, ORDINAL_POSITION
        """
        
        return self.run_sql(query)
