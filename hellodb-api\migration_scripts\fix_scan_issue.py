#!/usr/bin/env python3
"""
修复扫描文件不完整的问题
"""
import os
import sys

def update_migration_to_use_directory_scan():
    """更新迁移脚本直接从目录获取应用列表"""
    
    script_path = 'migration_scripts/full_migration.py'
    
    # 读取原文件
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建新的get_all_app_aliases函数
    new_function = '''def get_all_app_aliases():
    """获取所有应用的别名列表 - 直接从向量存储目录扫描"""
    try:
        # 方法1: 直接扫描向量存储目录
        chroma_path = '/data/chroma'
        if os.path.exists(chroma_path):
            all_dirs = [d for d in os.listdir(chroma_path) 
                       if os.path.isdir(os.path.join(chroma_path, d)) 
                       and not d.startswith('.')]  # 排除隐藏目录
            
            print(f"📁 从向量存储目录扫描到 {len(all_dirs)} 个应用")
            
            # 过滤掉明显的系统目录
            filtered_apps = []
            for app in all_dirs:
                # 排除数字目录和明显的系统目录
                if app.isdigit() or app in ['.cache', 'Stop_lm']:
                    print(f"   跳过系统目录: {app}")
                    continue
                filtered_apps.append(app)
            
            print(f"📊 过滤后的应用数量: {len(filtered_apps)}")
            return sorted(filtered_apps)
        else:
            print(f"❌ 向量存储目录不存在: {chroma_path}")
            
    except Exception as e:
        print(f"❌ 扫描向量存储目录失败: {str(e)}")
    
    # 备用方案: 尝试数据库查询
    try:
        print("⚠️  尝试从数据库获取应用列表...")
        from sqlalchemy import create_engine, text
        import os
        
        # 构建数据库连接URL
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'hellodb')
        db_user = os.getenv('DB_USER', 'postgres')
        db_password = os.getenv('DB_PASSWORD', 'password')
        
        db_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        engine = create_engine(db_url)
        with engine.connect() as conn:
            # 获取所有应用（不限制状态）
            result = conn.execute(text("""
                SELECT DISTINCT alias 
                FROM data_source_app 
                ORDER BY alias
            """))
            app_aliases = [row[0] for row in result.fetchall()]
            print(f"📊 从数据库获取到 {len(app_aliases)} 个应用")
            return app_aliases
    except Exception as e:
        print(f"❌ 从数据库获取应用列表失败: {str(e)}")
    
    # 最后备用方案: 使用扫描文件
    print("⚠️  使用扫描文件作为最后备用方案...")
    scan_file = 'migration_scripts/output/vector_data_scan_results.json'
    if os.path.exists(scan_file):
        import json
        with open(scan_file, 'r', encoding='utf-8') as f:
            scan_data = json.load(f)
        
        all_apps = list(scan_data.get('/data/chroma', {}).keys())
        print(f"📊 从扫描文件获取到 {len(all_apps)} 个应用")
        return sorted(all_apps)
    
    print("❌ 所有方法都失败了")
    return []'''
    
    # 找到原函数并替换
    import re
    
    # 匹配原函数（从def开始到下一个def或文件结尾）
    pattern = r'def get_all_app_aliases\(\):.*?(?=\ndef |\nclass |\n[a-zA-Z]|\Z)'
    
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, new_function, content, flags=re.DOTALL)
        
        # 写回文件
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新 {script_path}")
        print("   现在将直接从向量存储目录扫描应用")
        return True
    else:
        print(f"❌ 未找到get_all_app_aliases函数")
        return False

def test_new_function():
    """测试新的应用获取函数"""
    try:
        from migration_scripts.full_migration import get_all_app_aliases
        apps = get_all_app_aliases()
        
        print(f"\n📊 测试结果:")
        print(f"   获取到 {len(apps)} 个应用")
        
        # 检查kehu-clickhouse是否在列表中
        if 'kehu-clickhouse' in apps:
            print(f"   ✅ kehu-clickhouse 已包含在应用列表中")
        else:
            print(f"   ❌ kehu-clickhouse 仍然不在应用列表中")
        
        # 显示前10个应用
        print(f"   前10个应用: {apps[:10]}")
        
        return len(apps) > 70  # 期望超过70个应用
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 修复扫描文件不完整问题")
    print("=" * 50)
    
    print("问题分析:")
    print("- 向量存储目录中有84个应用")
    print("- 扫描文件中只有45个应用")
    print("- kehu-clickhouse等39个应用被遗漏")
    print()
    
    print("解决方案:")
    print("- 修改迁移脚本直接从向量存储目录获取应用列表")
    print("- 不再依赖不完整的扫描文件")
    print("- 添加数据库查询作为备用方案")
    print()
    
    # 更新迁移脚本
    print("1. 更新迁移脚本...")
    if update_migration_to_use_directory_scan():
        print("✅ 迁移脚本更新成功")
    else:
        print("❌ 迁移脚本更新失败")
        return
    
    # 测试新函数
    print("\n2. 测试新的应用获取函数...")
    if test_new_function():
        print("✅ 测试成功，应用数量正常")
    else:
        print("❌ 测试失败，应用数量仍然不正常")
        return
    
    print("\n3. 建议下一步操作:")
    print("   现在可以重新运行迁移:")
    print("   python migration_scripts/one_click_migrate.py --mode full")
    print()
    print("   或者先测试kehu-clickhouse:")
    print("   python migration_scripts/migration_tool.py kehu-clickhouse")

if __name__ == "__main__":
    main()
