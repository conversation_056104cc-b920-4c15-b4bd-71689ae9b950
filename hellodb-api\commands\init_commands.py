import json
import os
import click
from flask.cli import with_appcontext
from extensions.ext_database import db
from services.account_service import AccountService
from services.system.menu_service import MenuService
from models.account import Account
from models.system import Role, Menu

class InitializationService:
    @staticmethod
    def load_json_data(filename):
        file_path = os.path.join(os.path.dirname(__file__), '..', 'init_data', filename)
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    @staticmethod
    def init_admin():
        """初始化管理员账户"""
        if Account.query.first():
            click.echo('Account user already exists, skipping...')
            return

        admin_data = InitializationService.load_json_data('admin.json')
        user = AccountService.create_account(admin_data)
        click.echo('Admin account created successfully')
        return user

    @staticmethod
    def init_roles():
        """初始化角色"""
        if Role.query.first():
            click.echo('Roles already exist, skipping...')
            return

        roles_data = InitializationService.load_json_data('roles.json')
        for role_data in roles_data:
            Role.create(**role_data)
        click.echo('Roles created successfully')

    @staticmethod
    def init_menus():
        """初始化菜单"""
        if Menu.query.first():
            click.echo('Menus already exist, skipping...')
            return

        menus_data = InitializationService.load_json_data('menus.json')
        for menu_data in menus_data:
            MenuService.create_menu(menu_data)
        click.echo('Menus created successfully')

@click.command('init-system')
@click.option('--force', is_flag=True, help='Force initialization even if data exists')
@with_appcontext
def init_system_command(force):
    """Initialize system with default data."""
    try:
        if force:
            # 如果使用 force 参数，先清除现有数据
            Menu.query.delete()
            Role.query.delete()
            Account.query.delete()
            db.session.commit()
            click.echo('Existing data cleared')

        InitializationService.init_admin()
        InitializationService.init_roles()
        InitializationService.init_menus()

        click.echo('System initialization completed successfully')

    except Exception as e:
        click.echo(f'Error during initialization: {str(e)}')
        db.session.rollback()
        raise

