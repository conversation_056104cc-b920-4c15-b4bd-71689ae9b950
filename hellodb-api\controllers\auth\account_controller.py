from hashlib import md5
from flask import abort, jsonify, request
from flask_login import current_user, login_required
from flask_restful import marshal_with

from controllers.api_response import ApiResponse
from controllers.auth import api
from controllers.wraps import WebApiResource

from fields.account_fields import user_info_response_fields, account_api_key_response_fields
from services.account_service import AccountService


class AccountInfoApi(WebApiResource):

    @marshal_with(user_info_response_fields)
    @login_required
    def get(self):
        account = current_user

        account.roles = AccountService.get_user_roles(account)

        # return  {"code": 200, "msg": "用户信息获取成功", "data": account}
        return ApiResponse.success(data=account, msg="用户信息获取成功") 
    
    @marshal_with(user_info_response_fields)
    def put(self):
        account = AccountService.load_account(current_user.id)
        if account is None:
            abort(404)
            return ApiResponse.error(404, '用户不存在')
        
        account = AccountService.update_account(account, **request.get_json())
        return ApiResponse.success(data=account, msg="用户信息更新成功")
    

class AccountApiKeyApi(WebApiResource):

    @marshal_with(account_api_key_response_fields)
    @login_required
    def get(self):
        accounr_id = current_user.id
        # 对accounr_id进行一个加盐md5得到apikey
        apikey = md5(accounr_id.encode('utf-8')).hexdigest()
        return ApiResponse.success(data=apikey, msg="用户APIKEY获取成功")


api.add_resource(AccountInfoApi, '/account/info')
api.add_resource(AccountApiKeyApi, '/account/apikey')