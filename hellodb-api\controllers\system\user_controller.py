from flask import request
from flask_login import login_required, current_user
from flask_restful import marshal_with

from controllers.system import api
from controllers.wraps import WebApiResource
from controllers.api_response import ApiResponse
from fields.system_fields import user_fields, page_response_fields, common_response_fields, user_profile_response_fields
from models.account import Account, AccountStatus
from services.account_service import AccountService

class UserListApi(WebApiResource):
    
    @marshal_with(page_response_fields)
    @login_required
    def get(self):
        """获取用户列表"""
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        
        # 查询条件
        query = Account.query.filter(Account.status != AccountStatus.DELETED.value)
        
        # 总数
        total = query.count()
        
        # 分页
        accounts = query.order_by(Account.created_at.desc()).paginate(page=page, per_page=page_size)
        
        # 将用户对象转换为字典
        account_list = [account.to_dict() for account in accounts.items]
        
        return ApiResponse.success(data={
            'list': account_list,  # 返回转换后的用户列表
            'total': total
        })

    @marshal_with(common_response_fields)
    @login_required
    def post(self):
        """创建用户"""
        data = request.get_json()
        
        # 检查用户名是否已存在
        if AccountService.check_name_exists(data['username']):
            return ApiResponse.error(code=400, msg='用户名已存在')
            
        # 检查邮箱是否已存在
        if AccountService.check_email_exists(data['email']):
            return ApiResponse.error(code=400, msg='邮箱已存在')
            
        # 检查手机号是否已存在
        if data.get('mobile') and AccountService.check_mobile_exists(data['mobile']):
            return ApiResponse.error(code=400, msg='手机号已存在')
            
        # 创建用户
        account = Account(
            name=data['username'],
            nickname=data['nickname'],
            email=data['email'],
            mobile=data.get('mobile'),
            department_id=data.get('department_id'),
            role_id=data.get('role_id'),
            status=data.get('status', AccountStatus.ACTIVE.value)
        )
        
        from extensions.ext_database import db
        db.session.add(account)
        db.session.commit()
        
        return ApiResponse.success(msg='创建成功')

class UserApi(WebApiResource):
    
    @marshal_with(common_response_fields)
    @login_required
    def get(self, id):
        """获取用户详情"""
        account = Account.query.get_or_404(id)
        return ApiResponse.success(data=account)
    
    @marshal_with(common_response_fields)
    @login_required
    def put(self, id):
        """更新用户"""
        account = Account.query.get_or_404(id)
        data = request.get_json()
        
        # 检查用户名是否已存在
        if data.get('username') and data['username'] != account.name:
            if AccountService.check_name_exists(data['username'], exclude_id=id):
                return ApiResponse.error(code=400, msg='用户名已存在')
                
        # 检查邮箱是否已存在
        if data.get('email') and data['email'] != account.email:
            if AccountService.check_email_exists(data['email'], exclude_id=id):
                return ApiResponse.error(code=400, msg='邮箱已存在')

        # 检查手机号是否已存在
        if data.get('mobile') and data['mobile'] != account.mobile:
            if AccountService.check_mobile_exists(data['mobile'], exclude_id=id):
                return ApiResponse.error(code=400, msg='手机号已存在')
        
        # 更新用户信息
        update_data = {
            'name': data.get('username', account.name),
            'nickname': data.get('nickname', account.nickname),
            'email': data.get('email', account.email),
            'mobile': data.get('mobile', account.mobile),
            'department_id': data.get('department_id', account.department_id),
            'role_id': data.get('role_id', account.role_id),
            'status': data.get('status', account.status)
        }
        
        for key, value in update_data.items():
            setattr(account, key, value)
                
        from extensions.ext_database import db
        db.session.commit()
        return ApiResponse.success(msg='更新成功')
    
    @marshal_with(common_response_fields)
    @login_required
    def delete(self, id):
        """删除用户"""
        AccountService.delete_account(id)
        return ApiResponse.success(msg='删除成功')

class UserProfileApi(WebApiResource):
    @login_required
    @marshal_with(user_profile_response_fields)
    def get(self):
        """获取用户Profile数据"""
        # 获取请求参数中的account_id，如果没有则使用当前用户id
        account_id = request.args.get('account_id', current_user.id)
        
        # 如果不是管理员且查看的不是自己的profile，返回权限错误
        is_admin = AccountService.has_role(current_user, 'ADMIN')
        if not is_admin and str(account_id) != str(current_user.id):
            return ApiResponse.error('Permission denied'), 403
            
        # 使用AccountService获取用户profile数据
        profile_data = AccountService.get_account_profile(account_id)
        if not profile_data:
            return ApiResponse.error('User not found')
        else:
            return ApiResponse.success(data={
            'user': profile_data['account'],
            'recent_apps': profile_data['recent_apps'],
            'recent_datasources': profile_data['recent_datasources'],
            'recent_logs': profile_data['recent_logs'],
            'recent_messages': profile_data['recent_messages']
        })

# 注册路由
api.add_resource(UserListApi, '/api/system/users')
api.add_resource(UserApi, '/api/system/users/<string:id>')
api.add_resource(UserProfileApi, '/api/system/user/profile')