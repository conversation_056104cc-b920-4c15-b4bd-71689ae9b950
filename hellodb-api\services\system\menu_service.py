from models.system import Menu
from extensions.ext_database import db

class MenuService:
    @staticmethod
    def create_menu(data):
        """创建菜单"""
        # Ensure parent_id is None if it's an empty string
        parent_id = data.get('parent_id')
        if parent_id == '':
            parent_id = None
        
        menu = Menu(
            name=data['name'],
            path=data['path'],
            icon=data.get('icon'),
            sort=data.get('sort', 0),
            type=data['type'],
            permission=data.get('permission'),
            parent_id=parent_id,
            status=data.get('status', 'active')
        )
        
        db.session.add(menu)
        db.session.commit()
        return menu

    @staticmethod
    def update_menu(menu, data):
        """更新菜单信息"""
        for key, value in data.items():
            if hasattr(menu, key):
                setattr(menu, key, value)
        db.session.commit()

    @staticmethod
    def delete_menu(menu):
        """软删除菜单"""
        menu.status = 'deleted'
        db.session.commit() 