#!/usr/bin/env python3
"""
检查特定应用的详细情况
"""
import os
import sys
import json

def check_app_in_database(app_alias):
    """检查应用在数据库中的情况"""
    try:
        from sqlalchemy import create_engine, text
        
        # 构建数据库连接URL
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'hellodb')
        db_user = os.getenv('DB_USER', 'postgres')
        db_password = os.getenv('DB_PASSWORD', 'password')
        
        db_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        engine = create_engine(db_url)
        with engine.connect() as conn:
            # 查找特定应用
            result = conn.execute(text("""
                SELECT alias, status, created_at, updated_at, id
                FROM data_source_app 
                WHERE alias = :app_alias
            """), {"app_alias": app_alias})
            
            rows = result.fetchall()
            
            if rows:
                print(f"✅ 在数据库中找到应用 '{app_alias}':")
                for row in rows:
                    alias, status, created_at, updated_at, app_id = row
                    print(f"   ID: {app_id}")
                    print(f"   别名: {alias}")
                    print(f"   状态: {status}")
                    print(f"   创建时间: {created_at}")
                    print(f"   更新时间: {updated_at}")
                return True
            else:
                print(f"❌ 在数据库中未找到应用 '{app_alias}'")
                
                # 查找相似的应用名
                similar_result = conn.execute(text("""
                    SELECT alias, status 
                    FROM data_source_app 
                    WHERE alias LIKE :pattern
                    ORDER BY alias
                """), {"pattern": f"%{app_alias}%"})
                
                similar_apps = similar_result.fetchall()
                if similar_apps:
                    print(f"📋 找到相似的应用名:")
                    for alias, status in similar_apps:
                        print(f"   - {alias} ({status})")
                
                return False
                
    except Exception as e:
        print(f"❌ 数据库查询失败: {str(e)}")
        return False

def check_app_in_scan_file(app_alias):
    """检查应用在扫描文件中的情况"""
    scan_file = 'migration_scripts/output/vector_data_scan_results.json'
    
    if not os.path.exists(scan_file):
        print(f"❌ 扫描文件不存在: {scan_file}")
        return False
    
    try:
        with open(scan_file, 'r', encoding='utf-8') as f:
            scan_data = json.load(f)
        
        chroma_data = scan_data.get('/data/chroma', {})
        
        if app_alias in chroma_data:
            print(f"✅ 在扫描文件中找到应用 '{app_alias}':")
            app_data = chroma_data[app_alias]
            print(f"   SQL数据: {app_data.get('sql_count', 0)} 条")
            print(f"   DDL数据: {app_data.get('ddl_count', 0)} 条")
            print(f"   文档数据: {app_data.get('documentation_count', 0)} 条")
            print(f"   总计: {app_data.get('total_count', 0)} 条")
            return True
        else:
            print(f"❌ 在扫描文件中未找到应用 '{app_alias}'")
            
            # 查找相似的应用名
            similar_apps = [alias for alias in chroma_data.keys() if app_alias.lower() in alias.lower()]
            if similar_apps:
                print(f"📋 扫描文件中相似的应用名:")
                for alias in similar_apps:
                    print(f"   - {alias}")
            
            return False
            
    except Exception as e:
        print(f"❌ 读取扫描文件失败: {str(e)}")
        return False

def check_app_in_vector_storage(app_alias):
    """检查应用在向量存储中的情况"""
    old_path = f'/data/chroma/{app_alias}'
    new_path = f'/data/hellodb/chroma/{app_alias}'
    
    print(f"📁 检查向量存储目录:")
    
    # 检查旧目录
    if os.path.exists(old_path):
        try:
            files = os.listdir(old_path)
            print(f"   ✅ 旧目录存在: {old_path}")
            print(f"   📄 包含文件: {len(files)} 个")
            if files:
                print(f"   📋 文件列表: {files[:5]}{'...' if len(files) > 5 else ''}")
        except Exception as e:
            print(f"   ❌ 旧目录访问失败: {str(e)}")
    else:
        print(f"   ❌ 旧目录不存在: {old_path}")
    
    # 检查新目录
    if os.path.exists(new_path):
        try:
            files = os.listdir(new_path)
            print(f"   ✅ 新目录存在: {new_path}")
            print(f"   📄 包含文件: {len(files)} 个")
            if files:
                print(f"   📋 文件列表: {files[:5]}{'...' if len(files) > 5 else ''}")
        except Exception as e:
            print(f"   ❌ 新目录访问失败: {str(e)}")
    else:
        print(f"   ❌ 新目录不存在: {new_path}")

def check_migration_reports(app_alias):
    """检查迁移报告中是否提到这个应用"""
    output_dir = 'migration_scripts/output'
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    # 查找最新的迁移报告
    import glob
    report_files = glob.glob(f"{output_dir}/full_migration_report_*.json")
    
    if not report_files:
        print(f"❌ 未找到迁移报告文件")
        return
    
    latest_report = sorted(report_files)[-1]
    print(f"📊 检查最新迁移报告: {latest_report}")
    
    try:
        with open(latest_report, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 查找应用在报告中的记录
        results = report_data.get('results', [])
        found = False
        
        for result in results:
            if result.get('app_alias') == app_alias:
                found = True
                print(f"✅ 在迁移报告中找到应用 '{app_alias}':")
                print(f"   状态: {'成功' if result.get('success') else '失败'}")
                print(f"   操作: {result.get('action', 'unknown')}")
                if 'error' in result:
                    print(f"   错误: {result['error']}")
                if 'migration_stats' in result:
                    stats = result['migration_stats']
                    print(f"   迁移统计:")
                    print(f"     SQL: {stats.get('sql_migrated', 0)} 条")
                    print(f"     DDL: {stats.get('ddl_migrated', 0)} 条")
                    print(f"     文档: {stats.get('documentation_migrated', 0)} 条")
                break
        
        if not found:
            print(f"❌ 在迁移报告中未找到应用 '{app_alias}'")
            print(f"📋 报告中包含的应用数量: {len(results)}")
            
            # 显示前几个应用作为参考
            if results:
                print(f"📋 报告中的前5个应用:")
                for i, result in enumerate(results[:5]):
                    print(f"   {i+1}. {result.get('app_alias', 'unknown')}")
            
    except Exception as e:
        print(f"❌ 读取迁移报告失败: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python check_specific_app.py <应用别名>")
        print("示例: python check_specific_app.py kehu-clickhouse")
        sys.exit(1)
    
    app_alias = sys.argv[1]
    
    print(f"🔍 检查应用: {app_alias}")
    print("=" * 60)
    
    print("\n1. 检查数据库中的应用信息:")
    db_found = check_app_in_database(app_alias)
    
    print("\n2. 检查扫描文件中的应用信息:")
    scan_found = check_app_in_scan_file(app_alias)
    
    print("\n3. 检查向量存储目录:")
    check_app_in_vector_storage(app_alias)
    
    print("\n4. 检查迁移报告:")
    check_migration_reports(app_alias)
    
    print("\n5. 问题分析:")
    if not db_found:
        print("❌ 主要问题: 应用在数据库中不存在或名称不匹配")
        print("   可能原因:")
        print("   - 应用别名拼写错误")
        print("   - 应用在不同的数据库表中")
        print("   - 应用已被删除")
    elif not scan_found:
        print("❌ 主要问题: 应用在扫描文件中缺失")
        print("   可能原因:")
        print("   - 扫描过程中跳过了这个应用")
        print("   - 向量存储目录不存在或为空")
        print("   - 扫描脚本有bug")
    else:
        print("✅ 应用在数据库和扫描文件中都存在")
        print("   需要检查迁移过程中的具体错误")
    
    print("\n6. 建议解决方案:")
    if not db_found:
        print("   1. 检查应用别名是否正确")
        print("   2. 查询数据库中的所有应用: SELECT alias FROM data_source_app ORDER BY alias")
        print("   3. 确认应用是否在其他表中")
    elif not scan_found:
        print("   1. 重新运行向量数据扫描")
        print("   2. 检查向量存储目录是否存在")
        print("   3. 手动添加应用到扫描文件")
    else:
        print("   1. 查看详细的迁移日志")
        print("   2. 手动迁移这个特定应用")
        print("   3. 检查API配额和网络连接")

if __name__ == "__main__":
    main()
