import pandas as pd
from typing import Optional, Union

from core.vanna.hellodb_vanna import HellodbVanna
from vanna.exceptions import DependencyError, ImproperlyConfigured
from models.datasource import Datasource


class OracleHellodbVanna(HellodbVanna):
    """
    Oracle implementation of HellodbVanna.
    """
    
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to an Oracle database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            import oracledb
        except ImportError:
            raise DependencyError(
                "You need to install required dependencies to execute this method,"
                " run command: \npip install oracledb"
            )

        username = datasource.username
        if not username:
            raise ImproperlyConfigured("Please set your Oracle username")

        password = datasource.password
        if not password:
            raise ImproperlyConfigured("Please set your Oracle password")

        # For Oracle, we need a DSN (Data Source Name) which can be in the format:
        # host:port/service_name or host:port/SID
        # We'll try to construct it from the datasource fields
        
        host = datasource.host
        if not host:
            raise ImproperlyConfigured("Please set your Oracle host")

        port = datasource.port
        if not port:
            port = 1521  # Default Oracle port
        
        # Check if we have a service_name or SID
        service_name = datasource.database
        if not service_name:
            raise ImproperlyConfigured("Please set your Oracle service name or SID")
        
        # Construct the DSN
        dsn = f"{host}:{port}/{service_name}"
        
        return self.connect_to_oracle(username, password, dsn)
    
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected Oracle database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          In Oracle, this is typically the schema name.
                                          If None, uses the current user's schema.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        
        if database_name is None:
            # Get the current user's schema
            df_current_user = self.run_sql("SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') as schema_name FROM DUAL")
            database_name = df_current_user.iloc[0]['schema_name']
        
        # Get all columns from the Oracle data dictionary
        query = f"""
        SELECT 
            SYS_CONTEXT('USERENV', 'DB_NAME') AS table_catalog,
            c.OWNER AS table_schema, 
            c.TABLE_NAME as table_name, 
            c.COLUMN_NAME as column_name, 
            c.DATA_TYPE as data_type,
            cc.COMMENTS AS column_comment
        FROM 
            ALL_TAB_COLUMNS c
        LEFT JOIN 
            ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
        WHERE 
            c.OWNER = '{database_name}'
        ORDER BY 
            c.TABLE_NAME, c.COLUMN_ID
        """
        
        return self.run_sql(query)
