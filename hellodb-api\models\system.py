from datetime import datetime
from extensions.ext_database import db
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

class User(db.Model):
    __tablename__ = 'sys_users'
    
    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'))
    username = db.Column(db.String(50), unique=True, nullable=False, comment='用户名')
    nickname = db.Column(db.String(50), nullable=False, comment='昵称')
    email = db.Column(db.String(100), unique=True, nullable=False, comment='邮箱')
    phone = db.Column(db.String(20), unique=True, nullable=True, comment='手机号')
    department_id = db.Column(UUID, db.ForeignKey('sys_departments.id'), nullable=True, comment='部门ID')
    role_id = db.Column(UUID, db.<PERSON>('sys_roles.id'), nullable=True, comment='角色ID')
    status = db.Column(db.String(20), nullable=False, default='active', comment='状态：active-启用，inactive-禁用')
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment='更新时间')

    # 关联关系
    department = db.relationship('Department', backref='users')
    role = db.relationship('Role', backref='users')

    def to_dict(self):
        """将 User 对象转换为字典"""
        return {
            'id': str(self.id),  # UUID 转换为字符串
            'username': self.username,
            'nickname': self.nickname,
            'email': self.email,
            'phone': self.phone,
            'department_id': str(self.department_id) if self.department_id else None,
            'role_id': str(self.role_id) if self.role_id else None,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
        }

class Role(db.Model):
    __tablename__ = 'sys_roles'
    
    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'))
    name = db.Column(db.String(50), nullable=False, comment='角色名称')
    code = db.Column(db.String(50), unique=True, nullable=False, comment='角色编码')
    description = db.Column(db.String(200), nullable=True, comment='描述')
    status = db.Column(db.String(20), nullable=False, default='active', comment='状态：active-启用，inactive-禁用')
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment='更新时间')

    # 角色-权限多对多关系
    permissions = db.relationship('Permission', secondary='sys_role_permissions', backref='roles')

    def to_dict(self):
        """将 Role 对象转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
        }

class Permission(db.Model):
    __tablename__ = 'sys_permissions'
    
    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'))
    name = db.Column(db.String(50), nullable=False, comment='权限名称')
    code = db.Column(db.String(50), unique=True, nullable=False, comment='权限标识')
    type = db.Column(db.String(20), nullable=False, comment='类型：menu-菜单，button-按钮')
    status = db.Column(db.String(20), nullable=False, default='active', comment='状态：active-启用，inactive-禁用')
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment='更新时间')

    def to_dict(self):
        """将 Permission 对象转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'code': self.code,
            'type': self.type,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
        }

class RolePermission(db.Model):
    __tablename__ = 'sys_role_permissions'
    
    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'))
    role_id = db.Column(UUID, db.ForeignKey('sys_roles.id'), nullable=False)
    permission_id = db.Column(UUID, db.ForeignKey('sys_permissions.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')

    def to_dict(self):
        """将 RolePermission 对象转换为字典"""
        return {
            'id': str(self.id),
            'role_id': str(self.role_id),
            'permission_id': str(self.permission_id),
            'created_at': self.created_at.isoformat(),
        }

class Menu(db.Model):
    __tablename__ = 'sys_menus'
    
    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'))
    name = db.Column(db.String(50), nullable=False, comment='菜单名称')
    path = db.Column(db.String(200), nullable=False, comment='路由路径')
    icon = db.Column(db.String(50), nullable=True, comment='图标')
    sort = db.Column(db.Integer, nullable=False, default=0, comment='排序')
    type = db.Column(db.String(20), nullable=False, comment='类型：menu-菜单，button-按钮')
    permission = db.Column(db.String(50), nullable=True, comment='权限标识')
    parent_id = db.Column(UUID, db.ForeignKey('sys_menus.id'), nullable=True, comment='父级ID')
    status = db.Column(db.String(20), nullable=False, default='active', comment='状态：active-启用，inactive-禁用')
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment='更新时间')

    # 自关联
    parent = db.relationship('Menu', remote_side=[id], backref='children')

    def to_dict(self):
        """将 Menu 对象转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'path': self.path,
            'icon': self.icon,
            'sort': self.sort,
            'type': self.type,
            'permission': self.permission,
            'parent_id': self.parent_id,
            'status': self.status,
            'children': [child.to_dict() for child in self.children] if hasattr(self, 'children') else []
        }

class Department(db.Model):
    __tablename__ = 'sys_departments'
    
    id = db.Column(UUID, primary_key=True, server_default=db.text('uuid_generate_v4()'))
    name = db.Column(db.String(50), nullable=False, comment='部门名称')
    code = db.Column(db.String(50), unique=True, nullable=False, comment='部门编码')
    leader = db.Column(db.String(50), nullable=True, comment='负责人')
    phone = db.Column(db.String(20), nullable=True, comment='联系电话')
    email = db.Column(db.String(100), nullable=True, comment='邮箱')
    sort = db.Column(db.Integer, nullable=False, default=0, comment='排序')
    parent_id = db.Column(UUID, db.ForeignKey('sys_departments.id'), nullable=True, comment='父级ID')
    status = db.Column(db.String(20), nullable=False, default='active', comment='状态：active-启用，inactive-禁用')
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment='更新时间')

    # 自关联
    parent = db.relationship('Department', remote_side=[id], backref='children')

    def to_dict(self):
        """将 Department 对象转换为字典"""
        return {
            'id': str(self.id),
            'name': self.name,
            'code': self.code,
            'leader': self.leader,
            'phone': self.phone,
            'email': self.email,
            'sort': self.sort,
            'parent_id': str(self.parent_id) if self.parent_id else None,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
        } 