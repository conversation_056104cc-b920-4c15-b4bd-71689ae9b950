from flask import request
from flask_login import login_required
from flask_restful import marshal_with

from controllers.system import api
from controllers.wraps import WebApiResource
from controllers.api_response import ApiResponse
from fields.system_fields import role_fields, page_response_fields, common_response_fields
from models.system import Role, Permission, RolePermission
from extensions.ext_database import db

class RoleListApi(WebApiResource):
    
    @marshal_with(page_response_fields)
    @login_required
    def get(self):
        """获取角色列表"""
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        
        # 查询条件
        query = Role.query.filter(Role.status != 'deleted')
        
        # 总数
        total = query.count()
        
        # 分页
        roles = query.order_by(Role.created_at.desc()).paginate(page=page, per_page=page_size)
        
        return ApiResponse.success(data={
            'list': roles.items,
            'total': total
        })

    @marshal_with(common_response_fields)
    @login_required
    def post(self):
        """创建角色"""
        data = request.get_json()
        
        # 检查角色编码是否已存在
        if Role.query.filter_by(code=data['code']).first():
            return ApiResponse.error(code=400, msg='角色编码已存在')
            
        # 创建角色
        role = Role(
            name=data['name'],
            code=data['code'],
            description=data.get('description'),
            status=data.get('status', 'active')
        )
        
        db.session.add(role)
        db.session.commit()
        
        return ApiResponse.success(msg='创建成功')

class RoleApi(WebApiResource):
    
    @marshal_with(common_response_fields)
    @login_required
    def get(self, id):
        """获取角色详情"""
        role = Role.query.get_or_404(id)
        return ApiResponse.success(data=role)
    
    @marshal_with(common_response_fields)
    @login_required
    def put(self, id):
        """更新角色"""
        role = Role.query.get_or_404(id)
        data = request.get_json()
        
        # 检查角色编码是否已存在
        if data.get('code') and data['code'] != role.code:
            if Role.query.filter_by(code=data['code']).first():
                return ApiResponse.error(code=400, msg='角色编码已存在')
        
        # 更新角色信息
        for key, value in data.items():
            if hasattr(role, key):
                setattr(role, key, value)
                
        db.session.commit()
        return ApiResponse.success(msg='更新成功')
    
    @marshal_with(common_response_fields)
    @login_required
    def delete(self, id):
        """删除角色"""
        role = Role.query.get_or_404(id)
        role.status = 'deleted'  # 软删除
        db.session.commit()
        return ApiResponse.success(msg='删除成功')

class RolePermissionApi(WebApiResource):
    
    @marshal_with(common_response_fields)
    @login_required
    def get(self, id):
        """获取角色权限"""
        role = Role.query.get_or_404(id)
        permissions = [p.code for p in role.permissions]
        return ApiResponse.success(data={
            'permissions': permissions
        })
    
    @marshal_with(common_response_fields)
    @login_required
    def put(self, id):
        """更新角色权限"""
        role = Role.query.get_or_404(id)
        data = request.get_json()
        
        # 删除原有权限
        RolePermission.query.filter_by(role_id=id).delete()
        
        # 添加新权限
        if data.get('permissions'):
            permissions = Permission.query.filter(
                Permission.code.in_(data['permissions'])
            ).all()
            role.permissions = permissions
            
        db.session.commit()
        return ApiResponse.success(msg='更新成功')

# 注册路由
api.add_resource(RoleListApi, '/api/system/roles')
api.add_resource(RoleApi, '/api/system/roles/<string:id>')
api.add_resource(RolePermissionApi, '/api/system/roles/<string:id>/permissions') 