from typing import Optional

from werkzeug.exceptions import HTTPException


class BaseHTTPException(HTTPException):
    error_code: str = 'unknown'
    data: Optional[dict] = None

    def __init__(self, description=None, response=None):
        super().__init__(description, response)

        self.data = {
            "code": self.error_code,
            "message": self.description,
            "status": self.code,
        }


class DifyApiException(HTTPException):
    def __init__(self, response=None):

        description = f"Failed to request from remote: {response.status_code}, content: {response.content}"
        super().__init__(description)
        self.code = response.status_code

    
    def __init__(self, description=None, status_code=None):
        super().__init__(description)
        self.code = status_code