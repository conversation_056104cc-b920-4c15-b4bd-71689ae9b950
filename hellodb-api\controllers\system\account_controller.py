from flask import request
from flask_login import login_required
from flask_restful import marshal_with

from controllers.system import api
from services.account_service import AccountService
from controllers.wraps import WebApiResource
from controllers.api_response import ApiResponse
from extensions.ext_database import db

class AccountListApi(WebApiResource):
    
    @login_required
    def get(self):
        """获取账号列表"""
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))

        accounts_pagination = AccountService.list_accounts_by_page(page, page_size)

        return ApiResponse.paginated_from_pagination(accounts_pagination)


class AccountStatusApi(WebApiResource):

    @login_required
    def put(self, id):
        """禁用账号"""
        # 从request中获取status。可能是json，也可能是form
        status = request.json.get('status') if request.json else request.form.get('status')
        AccountService.change_account_status(id, status)
        return ApiResponse.success(msg='账号状态已变更')
        
        

# 注册路由
api.add_resource(AccountListApi, '/api/system/accounts')
api.add_resource(AccountStatusApi, '/api/system/accounts/<string:id>/status')
