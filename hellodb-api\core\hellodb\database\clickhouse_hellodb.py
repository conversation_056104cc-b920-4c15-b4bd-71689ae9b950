import pandas as pd
from typing import Optional, Union

from core.hellodb.exceptions import Validation<PERSON>rror
from core.hellodb.hellodb import Hellodb
from vanna.exceptions import DependencyError, ImproperlyConfigured
from models.datasource import Datasource


class ClickHouseHellodbVanna(Hellodb):
    """
    ClickHouse implementation of HellodbVanna.
    """
    
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a ClickHouse database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            import clickhouse_connect
        except ImportError:
            raise DependencyError(
                "You need to install required dependencies to execute this method,"
                " run command: \npip install clickhouse-connect"
            )

        host = datasource.host
        if not host:
            raise ImproperlyConfigured("Please set your ClickHouse host")

        port = datasource.port
        if not port:
            raise ImproperlyConfigured("Please set your ClickHouse port")

        dbname = datasource.database
        if not dbname:
            raise ImproperlyConfigured("Please set your ClickHouse database")

        username = datasource.username
        if not username:
            raise ImproperlyConfigured("Please set your ClickHouse user")

        password = datasource.password
        if not password:
            raise ImproperlyConfigured("Please set your ClickHouse password")
    
        conn = None

        try:
            conn = clickhouse_connect.get_client(
                host=host,
                port=port,
                username=username,
                password=password,
                database=dbname
            )
            print(conn)
        except Exception as e:
            raise ConnectionError(e)

        def run_sql_clickhouse(sql: str) -> Union[pd.DataFrame, None]:
            if conn:
                try:
                    result = conn.query(sql)
                    results = result.result_rows

                    # Create a pandas dataframe from the results
                    df = pd.DataFrame(results, columns=result.column_names)
                    return df

                except Exception as e:
                    raise e

        self.run_sql_is_set = True
        self.run_sql = run_sql_clickhouse

    
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected ClickHouse database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        
        if database_name is None:
            # Get the current database name
            df_current_db = self.run_sql("SELECT currentDatabase() as db_name")
            database_name = df_current_db.iloc[0]['db_name']
        
        # Get all columns from the information schema
        query = f"""
        SELECT 
            database as table_catalog,
            database as table_schema,
            table as table_name,
            name as column_name,
            type as data_type,
            default_expression as column_default,
            comment as column_comment
        FROM 
            system.columns
        WHERE 
            database = '{database_name}'
        ORDER BY 
            database, table, position
        """
        
        return self.run_sql(query)
