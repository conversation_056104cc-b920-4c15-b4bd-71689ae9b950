import pandas as pd
from typing import Optional

from core.vanna.hellodb_vanna import HellodbVanna
from vanna.exceptions import DependencyError, ImproperlyConfigured, ConnectionError
from models.datasource import Datasource


class OceanbaseHellodbVanna(HellodbVanna):
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to an OceanBase database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            import pymysql.cursors
        except ImportError:
            raise DependencyError(
                "You need to install required dependencies to execute this method,"
                " run command: \npip install PyMySQL"
            )

        host = datasource.host
        if not host:
            raise ImproperlyConfigured("Please set your OceanBase host")

        port = datasource.port
        if not port:
            raise ImproperlyConfigured("Please set your OceanBase port")

        dbname = datasource.database
        if not dbname:
            raise ImproperlyConfigured("Please set your OceanBase database")

        username = datasource.username
        if not username:
            raise ImproperlyConfigured("Please set your OceanBase user")

        password = datasource.password
        if not password:
            raise ImproperlyConfigured("Please set your OceanBase password")

        return self.connect_to_mysql(host, dbname, username, password, port, ssl_disabled=not datasource.ssl) 
    

    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected MySQL database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        
        if database_name is None:
            # Get the current database name
            df_current_db = self.run_sql("SELECT DATABASE() as db_name")
            database_name = df_current_db.iloc[0]['db_name']
        
        # Get all columns from the information schema
        query = f"""
        SELECT 
            '{database_name}' AS table_catalog,
            TABLE_SCHEMA as table_schema, 
            TABLE_NAME as table_name, 
            COLUMN_NAME as column_name, 
            DATA_TYPE as data_type, 
            COLUMN_COMMENT as column_comment
        FROM 
            INFORMATION_SCHEMA.COLUMNS 
        WHERE 
            TABLE_SCHEMA = '{database_name}'
        ORDER BY 
            TABLE_NAME, ORDINAL_POSITION
        """
        
        return self.run_sql(query)
