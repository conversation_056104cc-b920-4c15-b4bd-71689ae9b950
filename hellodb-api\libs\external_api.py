import logging
import re
import sys

from flask import current_app, got_request_exception
from flask_restful import Api, http_status_message
import psycopg2
import sqlalchemy
import vanna
from werkzeug.datastructures import Headers
from werkzeug.exceptions import HTTPException

from controllers.api_response import ApiResponse
from services.errors.account import AccountLoginError


class ExternalApi(Api):
    '''
    Api class that handles error handling for Flask-Restful resources
    '''

    def handle_error(self, e):
        """Error handler for the API transforms a raised exception into a Flask
        response, with the appropriate HTTP status code and body.

        :param e: the raised Exception object
        :type e: Exception

        """
        got_request_exception.send(current_app, exception=e)

        # 添加日志来追踪异常类型
        logging.error(f"Exception type: {type(e)}")
        logging.error(f"Exception args: {e.args}")
        logging.error(f"Exception details: {e}")


        # 这里处理自定义异常
        headers = Headers()
        if isinstance(e, HTTPException):
            if e.response is not None:
                resp = e.get_response()
                return resp

            status_code = e.code
            default_data = {
                'code': re.sub(r'(?<!^)(?=[A-Z])', '_', type(e).__name__).lower(),
                'msg': getattr(e, 'description', http_status_message(status_code)),
                'status': status_code
            }

            if default_data['msg'] and default_data['msg'] == 'Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)':
                default_data['msg'] = 'Invalid JSON payload received or JSON payload is empty.'

            headers = e.get_response().headers
        elif isinstance(e, ValueError):
            status_code = 200
            default_data = ApiResponse.error(msg=f'无效参数: {e.args}')
        elif isinstance(e, sqlalchemy.exc.StatementError):
            status_code = 200
            default_data = ApiResponse.error(msg=f'查询参数无效: {e.args}')
        elif isinstance(e, AccountLoginError):
            status_code = 200
            default_data = ApiResponse.error(msg=f'登录异常: {e.description}')
        elif isinstance(e, vanna.exceptions.ConnectionError):
            status_code = 200
            default_data = ApiResponse.error(msg=f'数据库连接异常: {e.args}')
        elif isinstance(e, psycopg2.errors.ForeignKeyViolation):
            status_code = 200
            default_data = ApiResponse.error(msg=f'外键约束异常: 删除时存在关联记录；或新增时关联记录不存在！')
        else:
            status_code = 500
            default_data = {
                'msg': http_status_message(status_code),
            }
            # default_data = {
            #     'msg': http_status_message(status_code),
            # }

        # Werkzeug exceptions generate a content-length header which is added
        # to the response in addition to the actual content-length header
        # https://github.com/flask-restful/flask-restful/issues/534
        remove_headers = ('Content-Length',)

        for header in remove_headers:
            headers.pop(header, None)

        data = getattr(e, 'data', default_data)

        error_cls_name = type(e).__name__
        if error_cls_name in self.errors:
            custom_data = self.errors.get(error_cls_name, {})
            custom_data = custom_data.copy()
            status_code = custom_data.get('status', 500)

            if 'msg' in custom_data:
                custom_data['msg'] = custom_data['msg'].format(
                    msg=str(e.description if hasattr(e, 'description') else e)
                )
            data.update(custom_data)

        # record the exception in the logs when we have a server error of status code: 500
        if status_code and status_code >= 500:
            exc_info = sys.exc_info()
            if exc_info[1] is None:
                exc_info = None
            current_app.log_exception(exc_info)

        if status_code == 406 and self.default_mediatype is None:
            # if we are handling NotAcceptable (406), make sure that
            # make_response uses a representation we support as the
            # default mediatype (so that make_response doesn't throw
            # another NotAcceptable error).
            supported_mediatypes = list(self.representations.keys())  # only supported application/json
            fallback_mediatype = supported_mediatypes[0] if supported_mediatypes else "text/plain"
            data = {
                'code': 'not_acceptable',
                'msg': data.get('msg')
            }
            resp = self.make_response(
                data,
                status_code,
                headers,
                fallback_mediatype = fallback_mediatype
            )
        elif status_code == 400:
            if isinstance(data.get('msg'), dict):
                param_key, param_value = list(data.get('msg').items())[0]
                data = {
                    'code': 'invalid_param',
                    'msg': param_value,
                    'params': param_key
                }
            else:
                if 'code' not in data:
                    data['code'] = '400'

            resp = self.make_response(data, status_code, headers)
        else:
            if 'code' not in data:
                data['code'] = '500'

            resp = self.make_response(data, status_code, headers)

        if status_code == 401:
            resp = self.unauthorized(resp)
        return resp