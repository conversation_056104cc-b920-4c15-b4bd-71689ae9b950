from flask import request
from flask_login import login_required
from flask_restful import marshal_with

from controllers.api_response import ApiResponse
from controllers.system import api
from controllers.wraps import WebApiResource
from models.datasource import Datasource
from extensions.ext_database import db

class UploadDatasourceencryptedPassword(WebApiResource):

    def get(self):

        datasources = Datasource.query.all()

        updated_count = 0

        for datasource in datasources:
            if datasource.encrypted_password is None:
                datasource.encrypted_password = datasource.password
                db.session.commit()
                updated_count += 1
                print(f"Updated {datasource.name}")
            else:
                print(f"{datasource.name} already updated.")
                continue

        datasources2 = Datasource.query.all()

        return ApiResponse.success(datasources2)

api.add_resource(UploadDatasourceencryptedPassword, '/api/temp/datasource_encryptedpassword')