"""
ChatDB训练数据管理控制器

该模块提供了ChatDB系统的训练数据管理功能，用于维护和优化自然语言到SQL的转换能力。

主要功能：
1. 训练数据管理
   - 添加训练数据：将新的问题-SQL对添加到训练集
   - 删除训练数据：移除单条或批量训练数据
   - 获取训练数据：查看当前训练集内容
2. 训练计划执行
   - 启动训练任务：异步执行模型训练
   - 训练进度跟踪：监控训练状态

接口列表：
- POST /api/v0/get_training_data              获取训练数据列表
- POST /api/v0/remove_training_data           删除单条训练数据
- POST /api/v0/batch_remove_training_data     批量删除训练数据
- POST /api/v0/remove_training_data_by_collection_name 按集合名删除训练数据
- POST /api/v0/add_training_data              添加训练数据
- PUT  /api/v0/add_training_data              更新训练数据
- POST /api/v0/datasources/<id>/start_train_plan 启动训练计划

依赖：
- Flask：Web框架
- Flask-Login：用户认证
- Flask-RESTful：REST API支持
- OpenAI：大语言模型接口
- Celery：异步任务处理
- Pandas：数据处理

Author: guoweiwei
Date: 2025-03-12
- OpenAI：用于自然语言处理
- Vanna：用于SQL生成和数据处理
- Plotly：用于数据可视化
- Pandas：用于数据处理

Author: guoweiwei
Date: 2025-03-12
"""
import logging
from flask import request
from flask import current_app as app
from flask_login import current_user, login_required
from openai import OpenAI
from config import Config
from controllers import api

from functools import wraps
from controllers.api_response import ApiResponse
from controllers.wraps import Resource, license_required

from core.cache import MemoryCache


from core.hellodb.hellodb import Hellodb
from core.hellodb.hellodb_factory import HellodbFactory
from core.vanna.hellodb_vanna import HellodbVanna
from core.vanna.hellodb_vanna_factory import HellodbVannaFactory

from models.datasource import DataSourceApp, ChatdbMessage
from services.datasource_service import DatasourceService
from services.chatdb_message_service import ChatdbMessageService
from tasks.chatdb_train_database_task import train_vanna_task

def get_app_info_by_id(app_id: str) -> DataSourceApp:
    appInfo = DatasourceService.get_app_by_id(app_id)
    return appInfo

def get_vanna_client_old(datasourceApp: DataSourceApp = None) -> HellodbVanna:
    config = Config()
    VANNA_LLM_MODEL = config.VANNA_LLM_MODEL
    VANNA_LLM_API_KEY = config.VANNA_LLM_API_KEY
    VANNA_LLM_BASE_URL = config.VANNA_LLM_BASE_URL

    if datasourceApp is None:
        path = '/data/chroma/default'
        initial_prompt = ''
    else:
        path = f'/data/chroma/{datasourceApp.alias}'
        initial_prompt = datasourceApp.initial_prompt

    vanna_config = {
        'path' : path,
        'model': VANNA_LLM_MODEL,
        'api_key': VANNA_LLM_API_KEY,
        'initial_prompt': initial_prompt,
        'language': '中文'
    }

    client = OpenAI(api_key=VANNA_LLM_API_KEY, base_url=VANNA_LLM_BASE_URL)

    vn = HellodbVannaFactory.create_vanna(datasourceApp, client=client, config=vanna_config)

    return vn

def get_vanna_client(datasourceApp: DataSourceApp = None) -> Hellodb:
    return HellodbFactory.create_hellodb(datasourceApp)



class GetTrainingDataApi(Resource):
    @license_required
    def post(self):
        app_id = request.args.get('app_id') or request.json.get('app_id')
        dataSourceApp = get_app_info_by_id(app_id)
        vn = get_vanna_client(dataSourceApp)
        df = vn.get_training_data()

        return ApiResponse.success({
            "type": "df",
            "id": "training_data",
            "df": df.head(1000).to_dict(orient='records'),
        })


class RemoveTrainingDataApi(Resource):
    @license_required
    def post(self):
        data = request.get_json()
        id = data.get('id')

        dataSourceApp = get_app_info_by_id(data.get('app_id'))
        vn = get_vanna_client(dataSourceApp)
        if id is None:
            return ApiResponse.error(400, "No id provided")
        if vn.remove_training_data(id=id):
            return ApiResponse.success({"success": True})
        else:
            return ApiResponse.error(500, "Couldn't remove training data")


class BatchRemoveTrainingDataApi(Resource):
    @license_required
    def post(self):
        data = request.get_json()
        ids = data.get('ids')

        dataSourceApp = get_app_info_by_id(data.get('app_id'))
        vn = get_vanna_client(dataSourceApp)
        if ids is None:
            return ApiResponse.error(400, "No ids provided")
        for id in ids:
            vn.remove_training_data(id=id)
        return ApiResponse.success({"success": True})
    

class RemoveTrainingDataByCollectionNameApi(Resource):
    @license_required
    def post(self):
        data = request.get_json()
        collection_name = data.get('collection_name')

        dataSourceApp = get_app_info_by_id(data.get('app_id'))
        vn = get_vanna_client(dataSourceApp)
        if type is None:
            return ApiResponse.error(400, "No type provided")
        vn.remove_collection(collection_name=collection_name)
        return ApiResponse.success({"success": True})


class AddTrainingDataApi(Resource):
    @license_required
    def post(self):
        data = request.get_json()
        question = data.get('question')
        sql = data.get('sql')
        ddl = data.get('ddl')
        documentation = data.get('documentation')

        dataSourceApp = get_app_info_by_id(data.get('app_id'))
        vn = get_vanna_client(dataSourceApp)
        try:
            id = vn.train(question=question, sql=sql, ddl=ddl,
                          documentation=documentation)
            return ApiResponse.success({"id": id})
        except Exception as e:
            return ApiResponse.error(500, str(e))
        

    @license_required
    def put(self):
        data = request.get_json()
        id = data.get('id')
        question = data.get('question')
        sql = data.get('sql')
        ddl = data.get('ddl')
        documentation = data.get('documentation')

        dataSourceApp = get_app_info_by_id(data.get('app_id'))
        vn = get_vanna_client(dataSourceApp)
        try:
            vn.update_training_data(id=id, question=question, sql=sql, ddl=ddl, documentation=documentation)
            return ApiResponse.success({"success": True})
        except Exception as e:
            return ApiResponse.error(500, str(e))


class StartTrainPlanApi(Resource):

    @license_required
    @login_required
    def post(self, id):
        # 在这里实现您的逻辑
        app_id = request.args.get('app_id') or request.json.get('app_id')
        dataSourceApp = get_app_info_by_id(app_id)

        # 异步执行 vn.train
        task = train_vanna_task.delay(
            app_id=dataSourceApp.id,
            datasource_id=id,
            account_id=current_user.id
        )
        logging.warning(f"Task sent successfully with Task ID: {task.id}， AppID： {dataSourceApp.id}, DataSourceID: {id} ,AccountID： {current_user.id}")

        return ApiResponse.success(data='训练任务已启动，请5-10分钟后查看训练数据')
    

class StartAppTrainPlanApi(Resource):
    @license_required
    @login_required
    def post(self):
        app_id = request.args.get('app_id') or request.json.get('app_id')
        if not app_id:
            return ApiResponse.error(400, "app_id is required")
            
        dataSourceApp = get_app_info_by_id(app_id)
        if not dataSourceApp:
            return ApiResponse.error(404, "App not found")

        # 异步执行 vn.train
        task = train_vanna_task.delay(
            app_id=dataSourceApp.id,
            datasource_id=dataSourceApp.datasource_id,
            account_id=current_user.id
        )
        logging.warning(f"Task sent successfully with Task ID: {task.id}， AppID： {dataSourceApp.id}, DataSourceID: {dataSourceApp.datasource_id}, AccountID： {current_user.id}")

        return ApiResponse.success(data='训练任务已启动，请5-10分钟后查看训练数据')

# Add resources to the API

api.add_resource(GetTrainingDataApi, '/api/v0/get_training_data')
api.add_resource(RemoveTrainingDataApi, '/api/v0/remove_training_data')
api.add_resource(BatchRemoveTrainingDataApi, '/api/v0/batch_remove_training_data')
api.add_resource(RemoveTrainingDataByCollectionNameApi, '/api/v0/remove_training_data_by_collection_name')
api.add_resource(AddTrainingDataApi, '/api/v0/add_training_data')
api.add_resource(StartTrainPlanApi, '/api/v0/datasources/<string:id>/start_train_plan')
api.add_resource(StartAppTrainPlanApi, '/api/v0/start_app_train_plan')