from flask_restful import fields
from libs.helper import TimestampField

menu_field = {  
    "id": fields.Integer,  
    "parentId": fields.Raw,  
    "title": fields.String,  
    "component": fields.String, 
    "path": fields.String,
    "icon": fields.String,
    "name": fields.String, 
    "keepAlive": fields.<PERSON>olean,  
    "locale": fields.String,
    "redirect": fields.String,
    "hideInMenu": fields.<PERSON><PERSON>an,  
    "hideInBreadcrumb": fields.<PERSON><PERSON>an,  
    "hideChildrenInMenu": fields.<PERSON>olean,
    "url": fields.String,
    "isIframe": fields.Boolean,

}  

# 定义外层的 data 字段和响应字段  
menu_response_fields = {  
    'code': fields.Integer,  
    'msg': fields.String,  
    'data': fields.Nested(menu_field)  # 这里嵌套 user 字段   
}  