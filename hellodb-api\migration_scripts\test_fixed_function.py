#!/usr/bin/env python3
"""
测试修复后的应用获取函数
"""
import sys
import os

# 强制重新加载模块
if 'migration_scripts.full_migration' in sys.modules:
    del sys.modules['migration_scripts.full_migration']

def test_get_all_app_aliases():
    """测试获取应用列表函数"""
    try:
        from migration_scripts.full_migration import get_all_app_aliases
        
        print("🔍 测试修复后的get_all_app_aliases函数")
        print("=" * 50)
        
        apps = get_all_app_aliases()
        
        print(f"\n📊 测试结果:")
        print(f"   获取到 {len(apps)} 个应用")
        
        # 检查kehu-clickhouse
        if 'kehu-clickhouse' in apps:
            print(f"   ✅ kehu-clickhouse 在应用列表中")
            index = apps.index('kehu-clickhouse')
            print(f"   位置: 第 {index + 1} 个")
        else:
            print(f"   ❌ kehu-clickhouse 不在应用列表中")
        
        # 显示前20个应用
        print(f"\n📋 前20个应用:")
        for i, app in enumerate(apps[:20]):
            marker = " ⭐" if app == 'kehu-clickhouse' else ""
            print(f"   {i+1:2d}. {app}{marker}")
        
        if len(apps) > 20:
            print(f"   ... 还有 {len(apps) - 20} 个应用")
        
        return apps
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def migrate_single_app_test(app_alias):
    """测试迁移单个应用"""
    print(f"\n🚀 测试迁移单个应用: {app_alias}")
    print("-" * 30)
    
    try:
        from migration_scripts.migration_tool import migrate_app
        
        result = migrate_app(app_alias)
        
        if result:
            print(f"✅ {app_alias} 迁移测试成功")
        else:
            print(f"❌ {app_alias} 迁移测试失败")
        
        return result
        
    except Exception as e:
        print(f"❌ 迁移测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 测试修复后的迁移工具")
    print("=" * 60)
    
    # 测试应用列表获取
    apps = test_get_all_app_aliases()
    
    if not apps:
        print("❌ 无法获取应用列表，退出测试")
        return
    
    # 如果kehu-clickhouse在列表中，测试迁移它
    if 'kehu-clickhouse' in apps:
        print(f"\n✅ kehu-clickhouse 在应用列表中，可以进行迁移")
        
        # 询问是否测试迁移
        response = input("是否测试迁移 kehu-clickhouse？(y/N): ").strip().lower()
        if response == 'y':
            migrate_single_app_test('kehu-clickhouse')
    else:
        print(f"\n❌ kehu-clickhouse 仍然不在应用列表中")
        print("可能的原因:")
        print("1. 函数修复没有生效")
        print("2. 目录扫描逻辑有问题")
        print("3. 模块缓存问题")
    
    print(f"\n📋 总结:")
    print(f"   应用总数: {len(apps)}")
    print(f"   预期数量: 80+ (从目录扫描)")
    print(f"   kehu-clickhouse: {'✅ 存在' if 'kehu-clickhouse' in apps else '❌ 缺失'}")
    
    if len(apps) >= 70:
        print(f"   ✅ 应用数量正常，修复成功")
    else:
        print(f"   ❌ 应用数量仍然偏少，修复可能未完全生效")

if __name__ == "__main__":
    main()
