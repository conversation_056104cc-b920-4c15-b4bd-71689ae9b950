import enum
import json

from flask_login import UserMixin
from sqlalchemy.dialects.postgresql import UUID

from extensions.ext_database import db


class AccountStatus(str, enum.Enum):
    PENDING = 'pending'
    UNINITIALIZED = 'uninitialized'
    ACTIVE = 'active'
    BANNED = 'banned'
    CLOSED = 'closed'


class Account(UserMixin, db.Model):
    __tablename__ = 'accounts'
    __table_args__ = (
        db.PrimaryKeyConstraint('id', name='account_pkey'),
        db.Index('account_email_idx', 'email')
    )

    id = db.Column(UUID, server_default=db.text('uuid_generate_v4()'))
    name = db.Column(db.String(255), nullable=False, comment='用户名')
    nickname = db.Column(db.String(255), nullable=True, comment='昵称')
    email = db.Column(db.String(255), nullable=True, comment='邮箱')
    email_verified_at = db.Column(db.DateTime, nullable=True, comment='邮箱验证时间')
    mobile = db.Column(db.String(255), nullable=True, comment='手机号')
    password = db.Column(db.String(255), nullable=True, comment='密码')
    password_salt = db.Column(db.String(255), nullable=True, comment='密码盐')
    avatar = db.Column(db.String(255), nullable=True, comment='头像')
    region = db.Column(db.String(255), nullable=True, comment='地区')
    interface_language = db.Column(db.String(255), nullable=True, server_default=db.text("'zh-cn'::character varying"), comment='界面语言')
    interface_theme = db.Column(db.String(255), nullable=True, server_default=db.text("'light'::character varying"), comment='界面主题')
    timezone = db.Column(db.String(255), nullable=True, server_default=db.text("'Asia/Shanghai'::character varying"), comment='时区')
    last_login_at = db.Column(db.DateTime, nullable=True, comment='最后登录时间')
    last_login_ip = db.Column(db.String(255), nullable=True, comment='最后登录IP')
    last_active_at = db.Column(
        db.DateTime, nullable=False, server_default=db.text('CURRENT_TIMESTAMP(0)'), comment='最后活跃时间')
    status = db.Column(db.String(16), nullable=False,
                       server_default=db.text("'active'::character varying"), comment='状态')
    initialized_at = db.Column(db.DateTime, nullable=True, comment='初始化时间')
    created_at = db.Column(db.DateTime, nullable=False,
                           server_default=db.text('CURRENT_TIMESTAMP(0)'), comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False,
                           server_default=db.text('CURRENT_TIMESTAMP(0)'), comment='更新时间')


    def to_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'nickname': self.nickname,
            'email': self.email,
            'email_verified_at': self.email_verified_at.strftime('%Y-%m-%d %H:%M:%S') if self.email_verified_at else None,
            'mobile': self.mobile,
            'avatar': self.avatar,
            'region': self.region,
            'interface_language': self.interface_language,
            'interface_theme': self.interface_theme,
            'timezone': self.timezone,
            'last_login_at': self.last_login_at.strftime('%Y-%m-%d %H:%M:%S') if self.last_login_at else None,
            'last_login_ip': self.last_login_ip,
            'last_active_at': self.last_active_at.strftime('%Y-%m-%d %H:%M:%S') if self.last_active_at else None,
            'status': self.status,
            'initialized_at': self.initialized_at.strftime('%Y-%m-%d %H:%M:%S') if self.initialized_at else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }

    @property
    def is_password_set(self):
        return self.password is not None

    def get_status(self) -> AccountStatus:
        status_str = self.status
        return AccountStatus(status_str)

    @classmethod
    def get_by_openid(cls, provider: str, open_id: str) -> db.Model:
        account_integrate = db.session.query(AccountIntegrate). \
            filter(AccountIntegrate.provider == provider, AccountIntegrate.open_id == open_id). \
            one_or_none()
        if account_integrate:
            return db.session.query(Account). \
                filter(Account.id == account_integrate.account_id). \
                one_or_none()
        return None

    def get_integrates(self) -> list[db.Model]:
        ai = db.Model
        return db.session.query(ai).filter(
            ai.account_id == self.id
        ).all()


class AccountIntegrate(db.Model):
    __tablename__ = 'account_integrates'
    __table_args__ = (
        db.PrimaryKeyConstraint('id', name='account_integrate_pkey'),
        db.UniqueConstraint('account_id', 'provider',
                            name='unique_account_provider'),
        db.UniqueConstraint('provider', 'open_id',
                            name='unique_provider_open_id')
    )

    id = db.Column(UUID, server_default=db.text('uuid_generate_v4()'))
    account_id = db.Column(UUID, nullable=False)
    provider = db.Column(db.String(16), nullable=False)
    open_id = db.Column(db.String(255), nullable=False)
    encrypted_token = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False,
                           server_default=db.text('CURRENT_TIMESTAMP(0)'))
    updated_at = db.Column(db.DateTime, nullable=False,
                           server_default=db.text('CURRENT_TIMESTAMP(0)'))


class InvitationCode(db.Model):
    __tablename__ = 'invitation_codes'
    __table_args__ = (
        db.PrimaryKeyConstraint('id', name='invitation_code_pkey'),
        db.Index('invitation_codes_batch_idx', 'batch'),
        db.Index('invitation_codes_code_idx', 'code', 'status')
    )

    id = db.Column(db.Integer, nullable=False)
    batch = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(32), nullable=False)
    status = db.Column(db.String(16), nullable=False,
                       server_default=db.text("'unused'::character varying"))
    used_at = db.Column(db.DateTime)
    used_by_account_id = db.Column(UUID)
    deprecated_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, nullable=False,
                           server_default=db.text('CURRENT_TIMESTAMP(0)'))


class PermissionType(enum.Enum):
    READ = 'read'
    WRITE = 'write'
    ADMIN = 'admin'

class ResourceType(enum.Enum):
    KNOWLEDGEBASE = 'knowledgebase'
    CHATBOT = 'chatbot'

class AccountPermission(db.Model):

    __tablename__ = 'account_permissions'
    __table_args__ = (
        db.PrimaryKeyConstraint('id', name='account_permission_pkey'),
        db.Index('index_account_resource', 'account_id', 'resource_type'),
        db.UniqueConstraint('resource_type', 'resource_id', 'account_id',
                            name='unique_account_permission')
    )

    id = db.Column(UUID, server_default=db.text('uuid_generate_v4()'))
    account_id = db.Column(UUID, nullable=False)
    resource_type = db.Column(db.String(32), nullable=False)
    resource_id = db.Column(UUID, nullable=False)
    permisson_type = db.Column(db.String(32), nullable=False)

    created_at = db.Column(db.DateTime, nullable=False,
                          server_default=db.text('CURRENT_TIMESTAMP(0)'))
    updated_at = db.Column(db.DateTime, nullable=False,
                          server_default=db.text('CURRENT_TIMESTAMP(0)'))
