<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>数据处理结果</title>
</head>
<body>
    <div id="output"></div>

    <script>
        var source = new EventSource("/table/add_embedding?table_name=yejibiao");
        source.onmessage = function(event) {
            console.log("Received data: " + event.data);
            var data = event.data;
            document.getElementById("output").innerText = data + "\n";
        };
        source.onopen = function() {
            console.log("Connection opened.");
        };
        source.onerror = function(err) {
            console.error('EventSource error:', err);
        };
        
        // 可以添加更多事件处理器，如 onopen 和 onerror
    </script>
</body>
</html>