import json
import time
from datetime import datetime, timezone
from flask import request, g
from flask_login import current_user
import re
from extensions.ext_database import db
from models.http_request_log import HttpRequestLog
from libs.helper import get_remote_ip

def get_os_from_user_agent(user_agent):
    """从User-Agent提取操作系统信息"""
    os_regex = {
        'Windows': r'Windows NT (\d+\.\d+)',
        'Mac': r'Mac OS X (\d+[._]\d+[._]?\d*)',
        'iOS': r'iOS (\d+[._]\d+[._]?\d*)',
        'Android': r'Android (\d+[._]\d+[._]?\d*)',
        'Linux': r'Linux'
    }
    
    for os_name, pattern in os_regex.items():
        match = re.search(pattern, user_agent)
        if match:
            if match.groups():
                return f"{os_name} {match.group(1).replace('_', '.')}"
            return os_name
    return "Unknown"

class RequestLoggerMiddleware:
    def __init__(self, app):
        self.app = app
        
        # 注册请求开始和结束的处理函数
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        
        # 排除不需要记录的路径
        self.exclude_paths = [
            '/health',
            '/static/',
            '/favicon.ico'
        ]
    
    def _get_account_id_security(self):
        try:
            return getattr(current_user, 'id', None) if current_user else None
        except Exception as e:
            return None

    def before_request(self):
        # 记录请求开始时间
        g.request_start_time = time.time()
    
    def after_request(self, response):
        # 检查是否需要排除此路径
        if any(request.path.startswith(path) for path in self.exclude_paths):
            return response
        
        try:
            # 计算请求处理时间
            duration_ms = int((time.time() - g.request_start_time) * 1000) if hasattr(g, 'request_start_time') else None
            
            # 安全获取 account_id，避免 AttributeError 或无效 token 的影响
            account_id = self._get_account_id_security()
            
            # 合并请求参数和请求体
            request_data = {}
            
            # 添加URL参数
            if request.args:
                request_data['url_params'] = dict(request.args)
            
            # 添加请求体
            if request.method in ['POST', 'PUT', 'PATCH']:
                if request.is_json:
                    # 使用 request.get_data() 获取原始数据，然后手动解析 JSON
                    raw_data = request.get_data(as_text=True)
                    if raw_data:
                        request_data['body'] = json.loads(raw_data)
                elif request.form:
                    request_data['form'] = dict(request.form)
                else:
                    try:
                        body_text = request.get_data(as_text=True)
                        if body_text:
                            request_data['raw_body'] = body_text
                    except:
                        request_data['raw_body'] = "Unable to parse request body"
            
            # 获取用户代理字符串
            user_agent_string = request.headers.get('User-Agent', '')
            
            # 创建日志记录
            log = HttpRequestLog(
                account_id=account_id,
                ip_address=get_remote_ip(request),
                path=request.path,
                method=request.method,
                request_data=json.dumps(request_data, ensure_ascii=False) if request_data else None,
                status_code=response.status_code,
                response_size=response.content_length,
                response_error_data=self._get_error_response_data(response),
                user_agent=user_agent_string,
                os_info=get_os_from_user_agent(user_agent_string) if user_agent_string else None,
                referer=request.referrer,
                duration_ms=duration_ms
            )
            
            # 保存到数据库
            db.session.add(log)
            db.session.commit()
        except Exception as e:
            # 记录错误但不影响响应
            self.app.logger.error(f"Error logging request: {str(e)}")
            db.session.rollback()
        
        return response
    
    def _get_error_from_json(self, json_obj):
        if json_obj and 'code' in json_obj and json_obj['code'] != 200:
            return json.dumps(json_obj, ensure_ascii=False)
        else:
            return ''
    
    def _get_error_response_data(self, response):

        if response.status_code >= 500:
            return f'error code: {response.status_code}, Internal Server Error'
        elif response.status_code >= 400 and response.status_code < 500:
            return f'error code: {response.status_code}, Bad Request'
        elif response.status_code >= 200 and response.status_code < 300:
            return self._get_error_from_json(response.json)
        else:
            return ''

def init_app(app):
    """初始化请求日志中间件"""
    RequestLoggerMiddleware(app)
