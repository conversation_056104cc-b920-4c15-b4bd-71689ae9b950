import logging
from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from config import Config

class SmsService:
    _client = None

    @classmethod
    def get_client(cls):
        """
        获取阿里云短信客户端实例
        """
        if cls._client is None:
            try:
                # 从配置中获取阿里云短信服务的配置
                config = Config()
                access_key_id = config.ALIYUN_SMS_ACCESS_KEY_ID
                access_key_secret = config.ALIYUN_SMS_ACCESS_KEY_SECRET

                # 配置阿里云访问凭证
                config = open_api_models.Config(
                    access_key_id=access_key_id,
                    access_key_secret=access_key_secret
                )
                # 访问的域名
                config.endpoint = 'dysmsapi.aliyuncs.com'

                # 创建客户端
                cls._client = Dysmsapi20170525Client(config)
            except Exception as e:
                logging.error(f"初始化阿里云短信客户端失败: {str(e)}")
                raise
        return cls._client

    @classmethod
    def send_verify_code(cls, mobile, code):
        """
        发送短信验证码
        :param mobile: 手机号
        :param code: 验证码
        :return: bool 是否发送成功
        """
        try:
            client = cls.get_client()

            # 从配置中获取短信模板信息
            config = Config()
            sign_name = config.ALIYUN_SMS_SIGN_NAME
            template_code = config.ALIYUN_SMS_TEMPLATE_CODE

            # 组装请求参数
            send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
                phone_numbers=mobile,
                sign_name=sign_name,
                template_code=template_code,
                template_param=f'{{"code":"{code}"}}'
            )

            # 创建运行时选项
            runtime = util_models.RuntimeOptions()

            # 发送短信
            response = client.send_sms_with_options(send_sms_request, runtime)

            # 记录响应日志
            logging.info(f"短信发送响应: {response.body}")

            # 判断是否发送成功
            if response.body.code == "OK":
                return True
            else:
                logging.error(f"短信发送失败: {response.body.message}")
                return False

        except Exception as e:
            logging.error(f"发送短信失败: {str(e)}")
            return False