import unittest
import pandas as pd
from unittest.mock import MagicMock, patch
from core.hellodb.hellodb_factory import HellodbFactory
from models.datasource import Datasource, DataSourceApp
from core.hellodb.llm.qwen_chat import <PERSON>wen_Chat
from core.hellodb.base import HellodbBase

import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, message="np.find_common_type is deprecated")

class TestHellodbMethods(unittest.TestCase):


    def cerate_hellodb(self):
        datasourceApp = DataSourceApp()
        datasourceApp.datasource_type = 'postgresql'
        datasourceApp.datasource = self.create_datasource()
        datasourceApp.alias = 'testdb'
        datasourceApp.created_by = 'test'
        return HellodbFactory.create_hellodb(datasourceApp)
    

    def create_datasource(self):
        datasource = Datasource()
        datasource.host = 'localhost'
        datasource.port = 5432
        datasource.database = 'hellodb'
        datasource.username = 'hellodb'
        datasource.password = 'hellodb#2025'
        datasource.ssl = False
        return datasource

    def test_01_connect_to_database_postgresql(self):
        
        hellodb = self.cerate_hellodb()
        
        hellodb.connect_to_database(self.create_datasource())

    def test_02_get_information_schema_postgresql(self):
        hellodb = self.cerate_hellodb()
        hellodb.connect_to_database(self.create_datasource())
        df = hellodb.get_information_schema()
        print(df)
        self.assertTrue(isinstance(df, pd.DataFrame))


    def test_03_training_db(self):
        hellodb = self.cerate_hellodb()

        hellodb.connect_to_database(self.create_datasource())
        df = hellodb.get_information_schema()
        plan = hellodb.get_training_plan_generic(df)
        hellodb.train(plan=plan)
        # 关键：写入后立即读取
        df2 = hellodb.get_training_data()
        print("after train, get_training_data:", df2)
        print("after train, get_training_data count:", len(df2))

    
    def test_04_get_training_data(self):
        hellodb = self.cerate_hellodb()

        df = hellodb.get_training_data()
        print("============= get training data =============")
        print(df)
        print("============= get training data =============")
        self.assertTrue(isinstance(df, pd.DataFrame))


    def test_05_get_related_documentation(self):
        hellodb = self.cerate_hellodb()
        hellodb.connect_to_database(self.create_datasource())
        docs = hellodb.get_related_documentation("有多少个accounts?")
        print("============= get_related_documentation =============")
        print(docs)
        print("============= get_related_documentation =============")
        self.assertTrue(isinstance(docs, list))


    # def test_06_generate_sql(self):
    #     hellodb = self.cerate_hellodb()
    #     hellodb.connect_to_database(self.create_datasource())
    #     sql = hellodb.generate_sql("有多少个账号?")
    #     print("============= generate_sql =============")
    #     print(sql)
    #     print("============= generate_sql =============")
    #     sql2 = hellodb.generate_sql("有多少个数据源相关的app?")
    #     print("============= generate_sql =============")
    #     print(sql2)
    #     print("============= generate_sql =============")


    def test_07_run_sql(self):
        hellodb = self.cerate_hellodb()
        hellodb.connect_to_database(self.create_datasource())
        sql = hellodb.generate_sql("datasources中有多少个本地数据源?也就是host是本地的", allow_llm_to_see_data=True)
        df = hellodb.run_sql(sql)
        print("============= run_sql =============")
        print(df)
        print("============= run_sql =============")



if __name__ == '__main__':
    import warnings  
    warnings.filterwarnings("ignore", category=DeprecationWarning, message="np.find_common_type is deprecated")  
    unittest.main()