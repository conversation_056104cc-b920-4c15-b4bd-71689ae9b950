"""
License服务模块，处理所有与License相关的业务逻辑
"""
import json
import datetime
from core.constants import LicenseInfo
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend

class LicenseService:
    """
    License服务类，提供License相关的所有操作
    """
    
    @classmethod
    def update_license_info(cls, customer, expiry_date, domain="", license_code=""):
        """
        更新License信息
        
        Args:
            customer: 客户名称
            expiry_date: 到期日期
            domain: 授权域名
            license_code: 注册码
        """
        LicenseInfo.CUSTOMER = customer
        LicenseInfo.EXPIRY_DATE = expiry_date
        LicenseInfo.IS_VALID = True
        LicenseInfo.DOMAIN = domain
        LicenseInfo.LICENSE_CODE = license_code
    
    @classmethod
    def get_license_info(cls):
        """
        获取License信息
        
        Returns:
            dict: 包含License信息的字典
        """
        return {
            "customer": LicenseInfo.CUSTOMER,
            "expiry_date": LicenseInfo.EXPIRY_DATE,
            "is_valid": LicenseInfo.IS_VALID,
            "domain": LicenseInfo.DOMAIN,
            "license_code": LicenseInfo.LICENSE_CODE
        }
        
    @classmethod
    def is_license_valid(cls):
        """
        检查License是否有效
        
        Returns:
            bool: License是否有效
        """
        return LicenseInfo.IS_VALID
    

    @classmethod
    def is_domain_valid(cls, request):
        """"
        检查当前请求的域名是否在授权范围内。
        优先从 X-Forwarded-Host 或 X-Original-Host 获取域名，这些通常包含反向代理前的原始域名。
        如果这些头不存在，则回退到使用 Host 头。
        
        Args:
            request: Flask请求对象
        
        Returns:
            bool: 当前请求的域名是否在授权范围内
        """
        # 按优先级尝试不同的头来获取域名
        domain = (
            request.headers.get("X-Forwarded-Host") or
            request.headers.get("X-Original-Host") or
            request.headers.get("Host", "")
        )
        return domain.lower() == LicenseInfo.DOMAIN.lower()
    

    PUBLIC_KEY_PEM = b'''
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr+s5ouBz0clqyyuaFrpf
cAH5goiK648HvknCG5oeIPGdM3lbYbimnPGUBDPC54MY+tUkrdZH7Gy/iJ0+0rEN
GQ7UbOwldxqKGZQWY/YT1S4KF1ipMBDQC9TXUi+vUaqUC/LIotQgUi7G8ZasQThh
3Gavinq3/qsBYM5P8UaUIX4375C+3Gmoq97FcBgfB81oBIkrMzBiIFoJbll4vlc2
i0AbjchpQosjfOCQ95lVTI2XRjrAUQZ73I57QhsS/8R3tEvnvlaEcQCZTwG1wXjI
ZnlNtwVy8dqyjTH28WLcMDkCOGRqrMeUeKeI64aWJ9i8UV8jsFPyD57a4In07KmU
HwIDAQAB
-----END PUBLIC KEY-----
'''

    @classmethod
    def _load_public_key(cls):
        return serialization.load_pem_public_key(cls.PUBLIC_KEY_PEM, backend=default_backend())

    @classmethod
    def get_license_path(cls):
        """
        获取license文件路径
        优先使用环境变量HELLODB_LICENSE_PATH，如果未设置则使用默认路径
        """
        import os
        return os.getenv('HELLODB_LICENSE_PATH', 'license.lic')

    @classmethod
    def verify_license(cls):
        try:
            license_path = cls.get_license_path()
            with open(license_path, "r") as f:
                license_package = json.load(f)
            
            license_data = license_package["data"]
            signature = bytes.fromhex(license_package["signature"])
            license_json = json.dumps(license_data, sort_keys=True)
            
            # 验证签名
            public_key = cls._load_public_key()
            public_key.verify(
                signature,
                license_json.encode(),
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256()
            )
            
            # 检查有效期
            expiry_date = datetime.datetime.strptime(license_data["expiry_date"], "%Y-%m-%d")
            if expiry_date < datetime.datetime.now():
                raise ValueError("License 已过期")
            
            # 更新License信息到常量类
            LicenseService.update_license_info(
                customer=license_data["customer"],
                expiry_date=license_data["expiry_date"],
                domain=license_data.get("domain", None),
                license_code=license_data.get("license_code", None)
            )
            
            # license_std_out_info = f'''
            # License校验通过！
            # 授权客户：{license_data["customer"]}
            # 授权到期时间：{license_data["expiry_date"]}
            # 授权域名：{license_data.get("domain", None)}
            # 授权码：{license_data.get("license_code", None)}
            # '''
            # print(license_std_out_info)
            return True
        except Exception as e:
            print(f"License 验证失败：{e}")
            return False
        