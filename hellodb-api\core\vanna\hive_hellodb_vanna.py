import pandas as pd
from typing import Optional

from core.vanna.hellodb_vanna import HellodbVanna
from vanna.exceptions import DependencyError, ImproperlyConfigured
from models.datasource import Datasource


class HiveHellodbVanna(HellodbVanna):
    """
    Hive implementation of HellodbVanna.
    """
    
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a Hive database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            from pyhive import hive
        except ImportError:
            raise DependencyError(
                "You need to install required dependencies to execute this method,"
                " run command: \npip install pyhive[hive]"
            )

        host = datasource.host
        if not host:
            raise ImproperlyConfigured("Please set your Hive host")

        port = datasource.port
        if not port:
            raise ImproperlyConfigured("Please set your Hive port")

        dbname = datasource.database
        if not dbname:
            raise ImproperlyConfigured("Please set your Hive database")

        username = datasource.username
        if not username:
            raise ImproperlyConfigured("Please set your Hive user")

        # Password might be optional for some Hive setups
        password = datasource.password

        return self.connect_to_hive(host, dbname, username, password, port)
    
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected Hive database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        
        if database_name is None:
            # Get the current database name
            df_current_db = self.run_sql("SELECT current_database() as db_name")
            database_name = df_current_db.iloc[0]['db_name']
        
        # First get all tables
        tables_query = f"SHOW TABLES IN {database_name}"
        tables_df = self.run_sql(tables_query)
        
        # Initialize an empty list to store all column information
        all_columns = []
        
        # For each table, get its columns
        for _, row in tables_df.iterrows():
            table_name = row[0]  # Assuming the table name is in the first column
            
            # Get columns for this table
            columns_query = f"DESCRIBE {database_name}.{table_name}"
            columns_df = self.run_sql(columns_query)
            
            # Add table information to each column
            for _, col_row in columns_df.iterrows():
                column_info = {
                    'table_catalog': database_name,  # Assuming the same catalog as the database'
                    'table_schema': database_name,
                    'table_name': table_name,
                    'column_name': col_row[0],  # col_name
                    'data_type': col_row[1],    # data_type
                    'column_comment': col_row[2] if len(col_row) > 2 else None  # comment
                }
                all_columns.append(column_info)
        
        # Convert to DataFrame
        return pd.DataFrame(all_columns)
