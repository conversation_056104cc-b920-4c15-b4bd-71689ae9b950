{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Celery Worker",
            "type": "debugpy",
            "request": "launch",
            "module": "celery",
            "args": [
                "-A", "app.celery", 
                "worker",
                "--loglevel=INFO", 
                "-P", "gevent", 
                "-Q", "chatdb", 
                "-E",
                "--purge"
            ],
            "console": "integratedTerminal",
            "justMyCode": false,  // 如果需要调试 Celery 内部代码，设为 false
            "env": {
                "GEVENT_SUPPORT": "True"  // 关键添加项
            }
        },
        {
            "name": "Debug Celery Beat",
            "type": "debugpy",
            "request": "launch",
            "module": "celery",
            "args": [
                "-A", "app.celery", 
                "beat",
                "--loglevel=INFO"
            ],
            "console": "integratedTerminal",
            "justMyCode": false,  // 如果需要调试 Celery 内部代码，设为 false
            "env": {
                "GEVENT_SUPPORT": "True"  // 关键添加项
            }
        },
        {
            "name": "Python Debugger: Flask",
            "type": "debugpy",
            "request": "launch",
            "module": "flask",
            "gevent": true,
            "env": {
                "FLASK_APP": "app.py",
                "FLASK_DEBUG": "1"
            },
            "args": [
                "run",
                "--no-debugger",
                "--no-reload"
            ],
            "jinja": true,
            "autoStartBrowser": false
        },
        {
            "name": "Debug Unittest",
            "type": "debugpy",
            "request": "launch",
            "module": "unittest",
            "args": ["core/hellodb/rag/hellodb_ragTest.py", "-v"],
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Debug Hellodb Unittest",
            "type": "debugpy",
            "request": "launch",
            "module": "unittest",
            "args": ["tests/test_hellodb.py", "-v"],
            "console": "integratedTerminal",
            "justMyCode": false
        }
    ]
}