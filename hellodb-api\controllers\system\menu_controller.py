from flask import request
from flask_login import login_required
from flask_restful import marshal_with

from controllers.system import api
from controllers.wraps import WebApiResource
from controllers.api_response import ApiResponse
from fields.system_fields import menu_fields, common_response_fields
from models.system import Menu
from extensions.ext_database import db
from services.system.menu_service import MenuService  # Import the new service

def build_tree(menus, parent_id=None):
    """构建树形结构"""
    tree = []
    for menu in menus:
        if menu.parent_id == parent_id:
            children = build_tree(menus, menu.id)
            if children:
                menu.children = children
            tree.append(menu)
    return tree


class MenuListApi(WebApiResource):
    
    @marshal_with(common_response_fields)
    @login_required
    def get(self):
        """获取菜单列表（树形结构）"""
        # 查询所有菜单
        menus = Menu.query.filter(Menu.status != 'deleted').order_by(Menu.sort).all()
        
        # 构建树形结构
        tree = build_tree(menus)
        
        # 将树形结构中的 Menu 对象转换为字典
        tree_dict = [menu.to_dict() for menu in tree]
        
        return ApiResponse.success(data=tree_dict)

    @marshal_with(common_response_fields)
    @login_required
    def post(self):
        """创建菜单"""
        data = request.get_json()
        
        # Use the MenuService to create the menu
        menu = MenuService.create_menu(data)
        
        return ApiResponse.success(msg='创建成功')

class MenuApi(WebApiResource):
    
    @marshal_with(common_response_fields)
    @login_required
    def get(self, id):
        """获取菜单详情"""
        menu = Menu.query.get_or_404(id)
        return ApiResponse.success(data=menu)
    
    @marshal_with(common_response_fields)
    @login_required
    def put(self, id):
        """更新菜单"""
        menu = Menu.query.get_or_404(id)  # 获取菜单实例
        data = request.get_json()  # 获取请求数据
        
        # 使用 MenuService 更新菜单
        MenuService.update_menu(menu, data)  # 传递实例和数据字典
        return ApiResponse.success(msg='更新成功')
    
    @marshal_with(common_response_fields)
    @login_required
    def delete(self, id):
        """删除菜单"""
        menu = Menu.query.get_or_404(id)
        
        # 检查是否有子菜单
        if Menu.query.filter_by(parent_id=id, status='active').first():
            return ApiResponse.error(code=400, msg='请先删除子菜单')
        
        # Use the MenuService to delete the menu
        MenuService.delete_menu(menu)
        return ApiResponse.success(msg='删除成功')

# 注册路由
api.add_resource(MenuListApi, '/api/system/menus')
api.add_resource(MenuApi, '/api/system/menus/<string:id>') 