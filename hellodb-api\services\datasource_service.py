import datetime
import random
from urllib.parse import quote_plus

import clickhouse_connect
import oracledb
from models.datasource import DataSourceAppStatistics, Datasource, DataSourceApp
from extensions.ext_database import db
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import create_engine
from sqlalchemy.exc import SQLAlchemyError

from services.errors.exception import DBTestConnectionException

class DatasourceService:
    @staticmethod
    def get_all_datasources(created_by: str):
        return Datasource.query.filter_by(created_by=created_by).order_by(Datasource.created_at.desc()).all()

    @staticmethod
    def get_datasource_by_id(datasource_id) -> Datasource:
        return Datasource.query.get(datasource_id)

    @staticmethod
    def create_datasource(data) -> Datasource:

        # data['encrypted_password'] = data.pop('password')
        # 暂时先同时保留password，把password复制一份到encrypted_password
        # data['encrypted_password'] = data['password']
        new_datasource = Datasource(**data)
        try:
            db.session.add(new_datasource)
            db.session.commit()
            return new_datasource
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e

    @staticmethod
    def update_datasource(datasource_id, data) -> Datasource:
        datasource = Datasource.query.get(datasource_id)
        if datasource:
            for key, value in data.items():
                if key == 'password' and not value:
                    continue  # 如果密码为空，跳过更新
                setattr(datasource, key, value)
            try:
                # datasource.encrypted_password = datasource.password
                # datasource.password = None
                # 暂时先同时保留password，把password复制一份到encrypted_password
                db.session.commit()
                return datasource
            except SQLAlchemyError as e:
                db.session.rollback()
                raise e
        return None

    @staticmethod
    def delete_datasource(datasource_id) -> bool:
        datasource = Datasource.query.get(datasource_id)
        if datasource:
            try:
                db.session.delete(datasource)
                db.session.commit()
                return True
            except SQLAlchemyError as e:
                db.session.rollback()
                raise e
        return False

    @staticmethod
    def test_connection(connection_info):
        try:
            # 根据数据源类型构建连接 URL
            encoded_password = quote_plus(connection_info['password'])

            # 特殊处理 ClickHouse 连接测试
            if connection_info['type'].lower() == 'clickhouse':
                # 使用 clickhouse_connect 进行连接测试
                client = clickhouse_connect.get_client(
                    host=connection_info['host'],
                    port=int(connection_info['port']),
                    username=connection_info['username'],
                    password=connection_info['password']
                )
                # 执行简单查询测试连接
                client.query('SELECT 1')
                client.close()
                return True
            elif  connection_info['type'].lower() == 'oracle':
                conn = oracledb.connect(user=connection_info['username'], 
                                        password=connection_info['password'], 
                                        dsn=connection_info['dsn'])
                cursor = conn.cursor()
                cursor.execute('SELECT 1')
                cursor.close()
                conn.close()
                return True

            if connection_info['type'].lower() == 'mysql':
                url = f"mysql+pymysql://{connection_info['username']}:{encoded_password}@{connection_info['host']}:{connection_info['port']}/{connection_info['database']}"
                if connection_info.get('ssl', False):
                    url += "?ssl=true"
            elif connection_info['type'].lower() == 'postgresql':
                url = f"postgresql://{connection_info['username']}:{encoded_password}@{connection_info['host']}:{connection_info['port']}/{connection_info['database']}"
                if connection_info.get('ssl', False):
                    url += "?ssl=true"
            elif connection_info['type'].lower() == 'sqlserver':
                # url = f"mssql+pyodbc://{connection_info['username']}:{encoded_password}@{connection_info['host']}:{connection_info['port']}/{connection_info['database']}?driver=ODBC Driver 17 for SQL Server"
                # if connection_info.get('ssl', False):
                #     url += "&encrypt=yes&trustServerCertificate=yes"
                # else:
                #     url += "&encrypt=no"
                url = f"mssql+pymssql://{connection_info['username']}:{encoded_password}@{connection_info['host']}:{connection_info['port']}/{connection_info['database']}?tds_version={connection_info['tds_version']}"
            elif connection_info['type'].lower() == 'hive':
                url = f"hive://{connection_info['username']}:{encoded_password}@{connection_info['host']}:{connection_info['port']}/{connection_info['database']}"
            elif connection_info['type'].lower() == 'sqlite':
                url = f"sqlite:///{connection_info['url']}"
            elif connection_info['type'].lower() == 'oceanbase':
                url = f"mysql+pymysql://{connection_info['username']}:{encoded_password}@{connection_info['host']}:{connection_info['port']}/{connection_info['database']}?charset=utf8mb4"
                if connection_info.get('ssl', False):
                    url += "&ssl=true"
            else:
                raise ValueError("不支持的数据源类型，如需更多数据库支持，请联系作者")

            # 创建引擎并尝试连接
            engine = create_engine(url)
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            return True
        except Exception as e:
            raise DBTestConnectionException(str(e))

    @staticmethod
    def get_app_list(created_by: str):
        """获取应用列表"""
        apps = DataSourceApp.query.filter_by(created_by=created_by).order_by(DataSourceApp.created_at.desc()).all()
        return [app.to_dict() for app in apps]
    
    @staticmethod
    def create_app(name: str, alias: str, description: str, datasource_id: str, datasource_type: str ,created_by: str, initial_prompt: str = None):
        """创建应用"""

        if not name:
            raise ValueError("应用名称不能为空")
        if not alias:
            raise ValueError("应用别名不能为空")
        else:
            if DatasourceService.get_app_by_alias(alias):
                raise ValueError("应用别名已存在,请重新更换一个别名")

        app = DataSourceApp(
            name=name,
            alias=alias,
            description=description,
            datasource_id=datasource_id,
            datasource_type=datasource_type,
            created_by=created_by,
            initial_prompt=initial_prompt
        )

        # 生成app_key
        app.app_key = DatasourceService.generate_app_key()
        app.created_at = datetime.datetime.now()

        db.session.add(app)
        db.session.commit()
        return app

    @staticmethod
    def generate_app_key():
        """生成应用密钥"""
        return ''.join(random.choices('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', k=32))

    @staticmethod
    def get_app_by_id(id: str) -> DataSourceApp:
        return DataSourceApp.query.filter_by(id=id).first()

    @staticmethod
    def get_app_by_alias(alias: str) -> DataSourceApp:
        """根据别名获取应用"""
        app = DataSourceApp.query.filter_by(alias=alias).first()
        if app:
            return app
        else:
            return None
        
    @staticmethod
    def get_app_by_appkey(app_key: str) -> DataSourceApp:
        """根据app_key获取应用"""
        app = DataSourceApp.query.filter_by(app_key=app_key).first()
        if app:
            return app
        else:
            return None


    @staticmethod
    def update_app(id: str, name: str, alias: str, description: str, datasource_id: str, datasource_type: str, updated_by: str, initial_prompt: str) -> DataSourceApp:
        """更新应用"""
        app = DataSourceApp.query.filter_by(id=id).first()
        if app:
            app.name = name
            app.alias = alias
            app.description = description
            app.datasource_id = datasource_id
            app.datasource_type = datasource_type
            app.updated_by = updated_by
            app.updated_at = datetime.datetime.now()
            app.initial_prompt = initial_prompt
            db.session.commit()
            return app
        
    @staticmethod
    def update_app_key(id: str) -> DataSourceApp:
        """更新应用密钥"""
        app = DataSourceApp.query.filter_by(id=id).first()
        if app:
            app.app_key = DatasourceService.generate_app_key()
            db.session.commit()
            return app
        else:
            return None
        
    @staticmethod
    def get_app_statistics(app_id: str) -> dict:
        """获取应用统计信息"""
        app_statistics = DataSourceAppStatistics.query.filter_by(app_id=app_id).first()
        if app_statistics:
            return app_statistics.to_dict()
        else:
            return None

        