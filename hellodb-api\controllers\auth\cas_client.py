import json
import logging
import pprint
import sys
from urllib.parse import urljoin, urlparse, urlencode
from flask import Flask, abort, request, session, redirect, url_for, current_app as app
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user


from cas import CASClient

from extensions.ext_login import login_manager

from services.account_service import AccountService
from . import bp

def get_cas_client():
    logging.warn('get_cas_client')
    logging.warn('CAS_SERVICE_URL: %s', app.config.get('CAS_SERVICE_URL'))
    logging.warn('CAS_SERVER_URL: %s', app.config.get('CAS_SERVER_URL'))

    return CASClient(
    version=2,
    service_url=app.config.get('CAS_SERVICE_URL'),
    server_url=app.config.get('CAS_SERVER_URL')
    )


def get_frontend_login_url():
    return app.config.get('FRONTEND_LOGIN_URL')

# 验证 URL 是否安全
def is_safe_url(target):
    if not target:
        return True
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and ref_url.netloc == test_url.netloc


# 自定义错误处理器来处理 Unauthorized 异常  
@login_manager.unauthorized_handler
def handle_unauthorized():  
    # 当 Flask-Login 抛出 Unauthorized 异常时，我们重定向到 CAS 登录页面  
    # 注意：这里你需要根据 CAS 客户端库来生成正确的登录 URL  
    # service_url = url_for('auth.ping', _external=True)  # 用户登录后应该被重定向回的 Flask 应用的 URL  
    return redirect(get_cas_client().get_login_url())  # 假设 cas.login_url 方法接受一个服务 URL  


# post方法
@bp.route('/login', methods=['GET'])
def cas_login():
    # service_url = url_for('auth.ping', _external=True)
    return redirect(get_cas_client().get_login_url())


# CAS回调视图（处理从CAS服务器重定向回来的请求）
@bp.route('/callback', methods=['GET', 'POST'])
def cas_callback():

    ticket = request.args.get('ticket')

    logging.warn('ticket: %s', ticket)
    logging.warn('request: %s', request)
    attributes = vars(get_cas_client())
    for attr_name, attr_value in attributes.items():
        logging.warn(f"{attr_name}: {attr_value}")

    if not ticket:
        return redirect(get_cas_client().get_login_url())

    username, attributes, pgtiou = get_cas_client().verify_ticket(ticket)


    if username:
        # 登录用户
        account = AccountService.load_account_by_username(username)

        logging.warn('account: %s', account)

        if not account:
            # 我们的数据库中不存在用户，就创建一个
            account = AccountService.create_account(name = username,
                                                    email = f'{username}@no-mail.com', 
                                                    password = '')

        AccountService.update_last_login(account, request)
        login_user(account)

        token = AccountService.get_account_jwt_token(account)

        logging.warn('token: %s', token)

        next = request.args.get('next')
        # is_safe_url 用来检查url是否可以安全的重定向。
        # 有关示例，参见 http://flask.pocoo.org/snippets/62/ 。
        if not is_safe_url(next):
            return abort(400)

        # 重定向到受保护的页面或首页
        # next = next or 'flask.url_for('index')'
        next = next or get_frontend_login_url()
        # return {"code": 200, "msg": "登陆成功", "data": {"token": token}}

        next_url = get_frontend_login_url() + "?token=" + token

        logging.warn('CAS login URL: %s', next_url) # 打印跳转的URL

        return redirect(next_url)

    else:

        return {"code": 400, "msg": "登陆失败,用户不存在"}


@bp.route('/logout')
def logout():
    logout_user()
    cas_logout_url = get_cas_client().get_logout_url()
    app.logger.debug('CAS logout URL: %s', cas_logout_url)
    return redirect(cas_logout_url)


@bp.route('/logout_callback')
def logout_callback():
    # redirect from CAS logout request after CAS logout successfully
    session.pop('username', None)
    return 'Logged out from CAS. <a href="/login">Login</a>'


@bp.route('/ping')
def ping():
    logging.warn('pong') # 打印测试pong
    return 'pong'
