#!/usr/bin/env python3
"""
验证修复是否生效
"""
import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🔍 验证修复是否生效")
    print("=" * 40)
    
    # 直接读取并执行修复后的函数
    try:
        # 读取full_migration.py文件
        script_path = os.path.join(os.path.dirname(__file__), 'full_migration.py')
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取get_all_app_aliases函数
        import re
        func_match = re.search(r'def get_all_app_aliases\(\):.*?(?=\ndef |\nclass |\n[a-zA-Z]|\Z)', content, re.DOTALL)
        
        if func_match:
            func_code = func_match.group(0)
            print("✅ 找到修复后的函数")
            
            # 执行函数
            exec(func_code)
            
            # 调用函数
            apps = get_all_app_aliases()
            
            print(f"\n📊 结果:")
            print(f"   获取到 {len(apps)} 个应用")
            
            if 'kehu-clickhouse' in apps:
                print(f"   ✅ kehu-clickhouse 在列表中")
                print(f"   🎯 修复成功！")
                
                # 显示包含kehu-clickhouse的前后几个应用
                index = apps.index('kehu-clickhouse')
                start = max(0, index - 3)
                end = min(len(apps), index + 4)
                
                print(f"\n   📋 kehu-clickhouse 周围的应用:")
                for i in range(start, end):
                    marker = " ⭐" if apps[i] == 'kehu-clickhouse' else ""
                    print(f"      {i+1:2d}. {apps[i]}{marker}")
                
                return True
            else:
                print(f"   ❌ kehu-clickhouse 仍然不在列表中")
                print(f"   🔧 修复可能未完全生效")
                return False
        else:
            print("❌ 未找到get_all_app_aliases函数")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 验证成功！")
        print(f"📋 下一步:")
        print(f"   1. 修复已生效，可以重新运行完整迁移")
        print(f"   2. 执行: python migration_scripts/full_migration.py")
        print(f"   3. 或使用: python migration_scripts/one_click_migrate.py --mode full")
    else:
        print(f"\n❌ 验证失败！")
        print(f"📋 需要进一步调查修复问题")
