from flask_restful import fields
from fields.custom_fields import CustomDateTime

chatdb_message_fields = {
    'id': fields.String,
    'app_id': fields.String,
    'question': fields.String,
    'answer_text': fields.String,
    'answer_sql': fields.String,
    'answer_sql_valid': fields.<PERSON><PERSON><PERSON>,
    'answer_data': fields.String,
    'answer_data_row_count': fields.Integer,
    'answer_charts': fields.String,
    'from_source': fields.String,
    'created_at': CustomDateTime,
    'created_by': fields.String,
    'is_recommend': fields.<PERSON><PERSON>an,
    'recommend_at': CustomDateTime,
    'recommend_by': fields.String
}

chatdb_messages_response_fields = {
    'code': fields.Integer,
    'msg': fields.String,
    'data': fields.Nested({
        'page': fields.Integer,
        'page_size': fields.Integer,
        'items': fields.List(fields.Nested(chatdb_message_fields)),
        'total': fields.Integer
    })
}

chatdb_appinfo_response_fields = {
    'code': fields.Integer,
    'msg': fields.String,
    'data': fields.Nested({
        'id': fields.String,
        'name': fields.String,
        'alias': fields.String,
        'description': fields.String,
        'datasource_id': fields.String,
        'datasource_type': fields.String,
        'initial_prompt': fields.String,
        'app_key': fields.String,
        'status': fields.String,
        'created_by': fields.String,
        'created_at': CustomDateTime,
        'updated_by': fields.String,
        'updated_at': CustomDateTime
    })

}