// Cursor Rules for hellodb-api

# General
- Ignore files and directories that are not source code (e.g., .git, .cursor, migration_scripts/output, migrations/versions).
- Do not suggest edits to files in `init_data/` unless specifically requested.

# Python
- Use Black formatting for all Python files.
- Use isort for import sorting.
- Enforce PEP8 style, except for line length (allow up to 120 chars).

# Tests
- All new features should have corresponding tests in `tests/`.
- Test files should be named `test_*.py`.

# Migrations
- Do not edit files in `migrations/` or `migration_scripts/` unless the user requests a migration or script change.

# Controllers/Services
- Place new API endpoints in the appropriate `controllers/` subdirectory.
- Place new business logic in the appropriate `services/` subdirectory.

# Models
- All database models should go in `models/`.
- Do not modify models without checking for corresponding migration needs.

# Extensions
- Place new Flask extensions in `extensions/`.

# Templates
- Place new HTML templates in `templates/` or its subfolders.

# Naming
- Use snake_case for Python files and functions.
- Use PascalCase for class names.

# Documentation
- Update `README.md` if new major features or setup steps are added.

# Requirements
- Add new dependencies to `requirements.txt` and, if needed, `migration_scripts/requirements.txt`.

# License
- Do not modify `license.lic`.

# Docker
- Update `Dockerfile` and `entrypoint.sh` if deployment or startup changes are made.

# Config
- Place new configuration in `config.py` or a new config file in the root directory.

# Deb/RPM sources
- Do not edit `debian.sources`, `debian.sources.aliyun`, or `sources.list` unless requested.

# Misc
- Do not commit secrets or credentials.
description:
globs:
alwaysApply: true
---
