"""
Chatdb管理控制器

该模块提供了Chatdb管理相关的 RESTful API 接口，包括：
- 应用列表管理
- 数据源列表管理
- 对话消息列表管理

Author: guoweiwei
Date: 2024-03-12
"""

from flask import request
from flask_login import current_user, login_required
from flask_restful import Resource, marshal_with
from services.chatdb_message_service import ChatdbMessageService
from services.datasource_service import DatasourceService
from fields.chatdb_fields import chatdb_messages_response_fields, chatdb_appinfo_response_fields
from controllers.api_response import ApiResponse
from controllers.system import api


class ChatdbAppListApi(Resource):
    """应用列表管理"""
    
    @login_required
    def get(self):
        """获取应用列表"""
        created_by = current_user.id
        apps = DatasourceService.get_app_list(created_by)
        return ApiResponse.success(data=apps)
    

class ChatdbAppInfoApi(Resource):
    """应用信息管理"""
    
    @login_required
    @marshal_with(chatdb_appinfo_response_fields)
    def get(self, app_id):
        """获取应用信息"""
        app = DatasourceService.get_app_by_id(app_id)
        return ApiResponse.success(data=app.to_dict())


class ChatdbDatasourceListApi(Resource):
    """数据源列表管理"""
    
    @login_required
    def get(self):
        """获取数据源列表"""
        created_by = current_user.id
        datasources = DatasourceService.get_all_datasources(created_by)
        return ApiResponse.success({
            'datasources': datasources,
            'total': len(datasources)
        })


class ChatdbMessagesApi(Resource):
    """对话消息列表管理"""

    @marshal_with(chatdb_messages_response_fields)
    @login_required
    def get(self):
        """获取对话消息列表"""
        app_id = request.args.get('app_id')
        sql_valid = request.args.get('sql_valid')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        is_recommend = request.args.get('is_recommend')
        page = int(request.args.get('page', 0))
        per_page = int(request.args.get('per_page', 10))
        
        query_result = ChatdbMessageService.query_message(
            app_id=app_id,
            sql_valid=sql_valid,
            start_date=start_date,
            end_date=end_date,
            is_recommend=is_recommend,
            page=page,
            per_page=per_page
        )
        return ApiResponse.paginated_from_pagination(query_result)


# 注册路由
api.add_resource(ChatdbAppListApi, '/api/system/chatdb/apps')
api.add_resource(ChatdbAppInfoApi, '/api/system/chatdb/app/<app_id>')
api.add_resource(ChatdbDatasourceListApi, '/api/system/chatdb/datasources')
api.add_resource(ChatdbMessagesApi, '/api/system/chatdb/messages')