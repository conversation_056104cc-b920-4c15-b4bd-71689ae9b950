###############################   启动时验证License    ###############################
import json
import sys
import datetime
from services.license_service import LicenseService
import os

if os.environ.get("DEBUG", "false").lower() != 'true':
    # 解决由于引入gevent而导致的ssl模块的SSLContext导致的递归调用错误（死循环），错误如下：
    # RecursionError: maximum recursion depth exceeded while calling a Python object
    from gevent import monkey
    # 只patch必要的模块
    #monkey.patch_socket()
    #monkey.patch_ssl()
    #monkey.patch_time()
    monkey.patch_all(dns=False) # 禁用 DNS 缓存
    import grpc.experimental.gevent
    grpc.experimental.gevent.init_gevent()


from flask_login import login_required, login_user
from controllers.api_response import ApiResponse
from services.errors.account import AccountLoginError

from flask import Flask, Response, jsonify, redirect, request, url_for, render_template
from flask_cors import CORS
from config import Config
from werkzeug.exceptions import Unauthorized

import json
import logging
import sys
from logging.handlers import RotatingFileHandler

# DO NOT REMOVE BELOW

from extensions import (
    ext_compress,
    ext_database,
    ext_login,
    ext_migrate,
    ext_sentry,
    ext_redis,
    ext_celery,
    ext_request_logger,
)
from extensions.ext_database import db
from extensions.ext_login import login_manager
from libs.passport import PassportService
from services.account_service import AccountService


class HelloDBApp(Flask):
    pass

def create_app() -> Flask:
    app = HelloDBApp(__name__, template_folder='templates')
    app.config.from_object(Config())
    
    app.config['JSON_AS_ASCII'] = False  # 这行虽然对 Flask-RESTful 不直接起效，但保持配置的一致性是好的做法
    app.config.update(RESTFUL_JSON=dict(ensure_ascii=False))
    app.json.ensure_ascii = False # 解决中文乱码问题  我靠，尝试了很多个，只有这个管用

    app.secret_key = app.config['SECRET_KEY']

    log_handlers = None
    log_file = app.config.get('LOG_FILE')
    if log_file:
        log_dir = os.path.dirname(log_file)
        os.makedirs(log_dir, exist_ok=True)
        log_handlers = [
            RotatingFileHandler(
                filename=log_file,
                maxBytes=1024 * 1024 * 1024,
                backupCount=5
            ),
            logging.StreamHandler(sys.stdout)
        ]

    logging.basicConfig(
        level=app.config.get('LOG_LEVEL'),
        format=app.config.get('LOG_FORMAT'),
        datefmt=app.config.get('LOG_DATEFORMAT'),
        handlers=log_handlers
    )

    initialize_extensions(app)
    register_blueprints(app)
    register_commands(app)

    # 启动时验证 license
    if not LicenseService.verify_license():
        print("License 无效或已过期，请在许可证管理页面上传有效的许可证!")

    print("\n\n=== HelloDB 启动成功 ===\n\n")
    logging.info("HelloDB 启动成功！")

    return app

def register_commands(app):
    from commands.init_commands import init_system_command
    app.cli.add_command(init_system_command)

def initialize_extensions(app):
    # Since the application instance is now created, pass it to each Flask
    # extension instance to bind it to the Flask application instance (app)
    
    ext_compress.init_app(app)
    ext_database.init_app(app)
    ext_migrate.init(app, db)
    ext_login.init_app(app)
    ext_sentry.init_app(app)
    ext_redis.init_app(app)
    ext_celery.init_app(app)
    ext_request_logger.init_app(app)


# Flask-Login configuration
# 注意：request_loader已在ext_login.py中注册，这里不再需要装饰器
# 实际的load_user_from_request函数已移至libs/login.py

@login_manager.user_loader
def load_user(user_id):
    return AccountService.load_user(user_id)   # 用load_user替换load_account


# @login_manager.unauthorized_handler
# def unauthorized_handler():
#     """Handle unauthorized requests."""
#     return Response(json.dumps({
#         'code': 'unauthorized',
#         'message': "Unauthorized."
#     }), status=401, content_type="application/json")


# register blueprint routers
def register_blueprints(app):
    from controllers.auth import bp as auth_bp
    from controllers.system import bp as system_bp
    from controllers.weixinmp.wechat_events_controller import wechat_events  # 添加微信事件蓝图

    CORS(auth_bp,
         allow_headers=['Content-Type', 'Authorization', 'X-App-Code'],
         methods=['GET', 'PUT', 'POST', 'DELETE', 'OPTIONS', 'PATCH']
         )
    app.register_blueprint(auth_bp)

    app.register_blueprint(system_bp)
    app.register_blueprint(wechat_events)  # 注册微信事件蓝图

    from controllers import bp as normal_bp
    app.register_blueprint(normal_bp)


# create app
app = create_app()
celery = app.extensions["celery"]

@app.errorhandler(AccountLoginError)
def handle_account_exception(e):
    return ApiResponse.error(msg=e.message)

# 捕获并处理非 HTTPException 的异常  注意，此处异常只会在flask api被处理，flask_restful 不会捕获到 
# flask_restful 异常请在libs/external_api.py 中处理 
# 捕获所有异常，包括HTTPException
@app.errorhandler(Exception)  
def handle_non_http_exception(e):  
    # 记录日志、发送通知等  
    app.logger.exception("发生错误: %s", e)
    
    error_message = "接口异常，请刷新当前页面，或稍后再试"
    
    # 检查请求是否期望JSON响应
    if request.is_json or request.accept_mimetypes.best == 'application/json':
        # 返回JSON格式的错误响应
        import libs.api_expection_handler
        resp = libs.api_expection_handler.handle_error(e)
        return resp
    else:
        # 返回HTML格式的错误页面
        return render_template('500.html', error=error_message), 500


@app.after_request
def after_request(response):
    """Add Version headers to the response."""
    response.set_cookie('remember_token', '', expires=0)
    response.headers.add('X-Version', app.config['CURRENT_VERSION'])
    response.headers.add('X-Env', app.config['DEPLOY_ENV'])
    return response


@app.route('/health')
def health():
    return Response(json.dumps({
        'status': 'ok',
        'version': app.config['CURRENT_VERSION']
    }), status=200, content_type="application/json")

@app.route('/')
def hello_world():  # put application's code here
    return 'Hello World!'


if __name__ == '__main__':
    app.run(debug=True , host='0.0.0.0', port=5000)