"""empty message

Revision ID: 8d212d7230d7
Revises: 528f8e1833e0
Create Date: 2025-06-12 13:49:31.137510

"""
from alembic import op
import sqlalchemy as sa
import sqlalchemy_utils  # 自动添加的依赖导入


# revision identifiers, used by Alembic.
revision = '8d212d7230d7'
down_revision = '528f8e1833e0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasources', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ssl', sa.<PERSON>(), nullable=True, comment='是否启用SSL连接'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasources', schema=None) as batch_op:
        batch_op.drop_column('ssl')

    # ### end Alembic commands ###
