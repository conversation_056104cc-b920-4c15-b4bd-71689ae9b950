import pandas as pd
from typing import Optional

import sqlite3

from core.vanna.hellodb_vanna import HellodbVanna
from vanna.exceptions import DependencyError, ImproperlyConfigured
from models.datasource import Datasource


class SQLiteHellodbVanna(HellodbVanna):
    """
    SQLite implementation of HellodbVanna.
    """
    
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a SQLite database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            import sqlite3
        except ImportError:
            raise DependencyError("SQLite is not installed. Please install it and try again.")
        
        url = datasource.url
        if not url:
            raise ImproperlyConfigured("Please set your SQLite URL")
        
        return self.connect_to_sqlite(url)

        
    
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected SQLite database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        
        if database_name is None:
            # Get the current database name
            df_current_db = self.run_sql("SELECT name as db_name FROM sqlite_master WHERE type='database'")
            database_name = df_current_db.iloc[0]['db_name']
        
        # Get all columns from the information schema
        query = f"""
        SELECT 
            '{database_name}' AS table_catalog,
            '{database_name}' AS table_schema,
            name AS table_name,
            type AS column_name,
            '' AS data_type,
            '' AS column_comment
        FROM 
            sqlite_master
        WHERE 
            type='table'
        ORDER BY 
            name
        """
        
        return self.run_sql(query)
