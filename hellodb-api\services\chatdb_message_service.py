import datetime
from models.datasource import ChatdbMessage
from extensions.ext_database import db
from sqlalchemy.exc import SQLAlchemyError

class ChatdbMessageService:

    @staticmethod
    def insert_message(data) -> ChatdbMessage:
        message = ChatdbMessage(**data)
        try:
            db.session.add(message)
            db.session.commit()
            return message
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e
        
    @staticmethod
    def insert_message_item(app_id, created_by, question, answer_text, answer_sql, answer_sql_valid, answer_data=None, answer_data_row_count=0, answer_charts=None,
                            from_source=ChatdbMessage.FromSource.WEB) -> ChatdbMessage:
        message = ChatdbMessage(
            app_id=app_id,
            question=question,
            answer_text=answer_text,
            answer_sql=answer_sql,
            answer_sql_valid=answer_sql_valid,
            answer_data=answer_data,
            answer_data_row_count=answer_data_row_count,
            answer_charts=answer_charts,
            created_by=created_by,
            created_at=datetime.datetime.now(),
            from_source=from_source
        )
        try:
            db.session.add(message)
            db.session.commit()
            return message
        except SQLAlchemyError as e:
            db.session.rollback()
            raise e

    @staticmethod
    def query_message(app_id, sql_valid=None, start_date=None, end_date=None, is_recommend=None, page=0, per_page=10):

        query = db.session.query(ChatdbMessage)
        if app_id is not None:
            query = query.filter(ChatdbMessage.app_id == app_id)
        if sql_valid is not None:
            query = query.filter(ChatdbMessage.answer_sql_valid == sql_valid)
        if start_date is not None:
            query = query.filter(ChatdbMessage.created_at >= start_date)
        if end_date is not None:
            query = query.filter(ChatdbMessage.created_at <= end_date)
        if is_recommend is not None:
            query = query.filter(ChatdbMessage.is_recommend == is_recommend)
        
        messages = query.order_by(ChatdbMessage.created_at.desc()).paginate(page=page, per_page=per_page, error_out=False)

        return messages
    