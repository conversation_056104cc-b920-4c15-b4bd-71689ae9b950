from flask_restful import fields
from fields.custom_fields import CustomDateTime

user_info_field = {  
    'id': fields.String,  # 因为使用了 UUID，但可以用字符串来表示  
    'name': fields.String,  
    'email': fields.String,
    'email_verified_at': CustomDateTime,
    'password': fields.String(attribute=None),  # 如果不想在 API 中暴露密码，可以设置 attribute=None  
    'password_salt': fields.String(attribute=None),  # 同样，如果不想在 API 中暴露 salt
    'mobile': fields.String,
    'avatar': fields.String,
    'region': fields.String,
    'interface_language': fields.String,  
    'interface_theme': fields.String,  
    'timezone': fields.String,  
    'last_login_at': CustomDateTime,  # 如果需要特定的日期时间格式  
    'last_login_ip': fields.String,  
    'last_active_at': CustomDateTime,  
    'status': fields.String,  
    'initialized_at': CustomDateTime,  
    'created_at': CustomDateTime,  
    'updated_at': CustomDateTime,
    'roles': fields.List(fields.String),
}  

# 定义外层的 data 字段和响应字段  
user_info_response_fields = {  
    'code': fields.Integer,  
    'msg': fields.String,  
    'data': fields.Nested(user_info_field)  # 这里嵌套 user 字段  
}

# apikey 返回的内容
account_api_key_response_fields =  {  
    'code': fields.Integer,  
    'msg': fields.String,  
    'data': fields.String  # 这里apikey字段  
}