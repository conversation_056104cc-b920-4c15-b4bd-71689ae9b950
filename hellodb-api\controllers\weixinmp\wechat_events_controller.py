from flask import Blueprint, request
from wechatpy.utils import check_signature
from wechatpy.exceptions import InvalidSignatureException
from wechatpy import parse_message, create_reply
from wechatpy.events import (
    SubscribeEvent, UnsubscribeEvent, ScanEvent,
    ScanCodePushEvent, ScanCodeWaitMsgEvent
)

wechat_events = Blueprint('wechat_events', __name__)

# 配置你的微信公众号信息
TOKEN = 'your_token'  # 需要替换为实际的 token

@wechat_events.route('/wechat', methods=['GET', 'POST'])
def handle_wechat():
    # 验证签名
    signature = request.args.get('signature', '')
    timestamp = request.args.get('timestamp', '')
    nonce = request.args.get('nonce', '')
    
    try:
        check_signature(TOKEN, signature, timestamp, nonce)
    except InvalidSignatureException:
        return 'Invalid signature', 403
    
    # 处理 GET 请求（用于服务器验证）
    if request.method == 'GET':
        echostr = request.args.get('echostr', '')
        return echostr
    
    # 处理 POST 请求（接收微信消息和事件）
    try:
        msg = parse_message(request.data)
        
        if msg.type == 'event':
            return handle_event(msg)
        
        # 如果不是事件消息，返回默认回复
        reply = create_reply('收到消息', msg)
        return reply.render()
    except Exception as e:
        return str(e), 500

def handle_event(msg):
    """处理微信事件"""
    if isinstance(msg, SubscribeEvent):
        # 处理关注事件
        if msg.scene:  # 通过扫描带参数二维码进行关注
            return create_reply(f'感谢关注！您是通过扫描二维码参数 {msg.scene_id} 关注的', msg).render()
        return create_reply('感谢关注！', msg).render()
    
    elif isinstance(msg, UnsubscribeEvent):
        # 处理取消关注事件
        # 这里可以添加取消关注后的业务逻辑，比如更新数据库等
        return 'success'
    
    elif isinstance(msg, ScanEvent):
        # 处理已关注用户扫描带参数二维码事件
        reply_content = f'您扫描了带参数的二维码，参数是：{msg.scene_id}'
        return create_reply(reply_content, msg).render()
    
    elif isinstance(msg, ScanCodePushEvent):
        # 处理扫码推事件
        result = msg.scan_code_info.scan_result
        return create_reply(f'扫码结果：{result}', msg).render()
    
    elif isinstance(msg, ScanCodeWaitMsgEvent):
        # 处理扫码推事件且弹出"消息接收中"提示框
        result = msg.scan_code_info.scan_result
        return create_reply(f'扫码结果（等待消息）：{result}', msg).render()
    
    # 其他事件类型的处理
    return create_reply('收到事件推送', msg).render()