#!/usr/bin/env python3
"""
测试新的Hellodb系统环境
"""
import os
import sys
import shutil

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.hellodb.vector.chromadb_vectorsotre import ChromaDB_VectorStore

def test_new_system():
    """测试新系统环境"""
    print("🔧 测试新的Hellodb系统环境...")
    
    # 清理测试目录
    test_dir = "migration_scripts/test_new_system"
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
    
    try:
        # 创建新系统实例
        config = {
            'path': test_dir,
            'dashscope_api_key': "sk-c4793b0c284e448ba1a611aa6f424062"
        }
        
        print("初始化ChromaDB_VectorStore...")
        vector_store = ChromaDB_VectorStore(config=config)
        print("✅ 初始化成功!")
        
        # 测试添加文档
        print("\n测试添加文档...")
        test_docs = [
            "这是一个测试文档，用于验证新系统的embedding功能。",
            "SELECT * FROM users WHERE age > 18;",
            "CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(100));"
        ]
        
        # 添加documentation
        doc_id = vector_store.add_documentation(test_docs[0])
        print(f"添加documentation成功: {doc_id}")
        
        # 添加SQL
        sql_id = vector_store.add_question_sql("查询成年用户", test_docs[1])
        print(f"添加SQL成功: {sql_id}")
        
        # 添加DDL
        ddl_id = vector_store.add_ddl(test_docs[2])
        print(f"添加DDL成功: {ddl_id}")
        
        # 测试检索
        print("\n测试检索功能...")
        
        # 测试documentation检索
        doc_results = vector_store.get_related_documentation("测试文档")
        print(f"Documentation检索结果: {len(doc_results)} 个")
        if doc_results:
            print(f"第一个结果: {doc_results[0]['text'][:50]}...")
        
        # 测试SQL检索
        sql_results = vector_store.get_similar_question_sql("查询用户")
        print(f"SQL检索结果: {len(sql_results)} 个")
        if sql_results:
            print(f"第一个结果: {sql_results[0]['text'][:50]}...")
        
        # 测试DDL检索
        ddl_results = vector_store.get_related_ddl("创建表")
        print(f"DDL检索结果: {len(ddl_results)} 个")
        if ddl_results:
            print(f"第一个结果: {ddl_results[0]['text'][:50]}...")
        
        # 测试数据统计
        print("\n测试数据统计...")
        print(f"SQL collection count: {vector_store.sql_collection.count()}")
        print(f"DDL collection count: {vector_store.ddl_collection.count()}")
        print(f"Documentation collection count: {vector_store.documentation_collection.count()}")
        
        print("\n✅ 新系统环境测试成功!")
        return True
        
    except Exception as e:
        print(f"\n❌ 新系统环境测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试目录
        if os.path.exists(test_dir):
            try:
                shutil.rmtree(test_dir)
                print(f"清理测试目录: {test_dir}")
            except:
                pass

if __name__ == "__main__":
    test_new_system()
