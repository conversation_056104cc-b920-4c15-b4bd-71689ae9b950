import pandas as pd
from typing import Optional, Union

from psycopg2 import sql

from core.hellodb.hellodb import Hellodb
from core.hellodb.exceptions import DependencyError, ImproperlyConfigured, ConnectionError, ValidationError
from models.datasource import Datasource


class PostgreSQLHellodb(Hellodb):
    """
    PostgreSQL implementation of HellodbVanna.
    """
    
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a PostgreSQL database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Raises:
            DependencyError: If required dependencies are not installed
            ImproperlyConfigured: If required connection parameters are missing
            ValidationError: If connection fails
        """
        try:
            import psycopg2
        except ImportError:
            raise DependencyError(
                "You need to install required dependencies to execute this method,"
                " run command: \npip install psycopg2-binary"
            )

        host = datasource.host
        if not host:
            raise ImproperlyConfigured("Please set your PostgreSQL host")

        port = datasource.port
        if not port:
            raise ImproperlyConfigured("Please set your PostgreSQL port")

        dbname = datasource.database
        if not dbname:
            raise ImproperlyConfigured("Please set your PostgreSQL database")

        username = datasource.username
        if not username:
            raise ImproperlyConfigured("Please set your PostgreSQL user")

        password = datasource.password
        if not password:
            raise ImproperlyConfigured("Please set your PostgreSQL password")
        
        try:
            conn = psycopg2.connect(host=host, port=port, user=username, password=password, database=dbname)
        except psycopg2.Error as e:
            raise ConnectionError(e)

        def run_sql_postgres(sql: str) -> Union[pd.DataFrame, None]:
            if conn:
                try:
                    cs = conn.cursor()
                    cs.execute(sql)
                    results = cs.fetchall()

                    # Create a pandas dataframe from the results
                    df = pd.DataFrame(results, columns=[desc[0] for desc in cs.description])
                    return df

                except psycopg2.Error as e:
                    if conn:
                        conn.rollback()
                        raise ValidationError(e)

                except Exception as e:
                            conn.rollback()
                            raise e

        self.dialect = "PostgreSQL"
        self.run_sql_is_set = True
        self.run_sql = run_sql_postgres

    
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected PostgreSQL database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema with columns and tables
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        # In PostgreSQL, we don't need to specify the database name in the query
        # as the connection is already to a specific database
        
        # Get all columns from the information schema
        # query = """
        # SELECT 
        #     table_schema, 
        #     table_name, 
        #     column_name, 
        #     data_type, 
        #     udt_name,
        #     is_nullable, 
        #     column_default,
        #     col_description(format('%s.%s', table_schema, table_name)::regclass::oid, ordinal_position) as column_comment
        # FROM 
        #     information_schema.columns
        # WHERE 
        #     table_schema NOT IN ('pg_catalog', 'information_schema')
        # ORDER BY 
        #     table_schema, table_name, ordinal_position
        # """

        query = '''
            SELECT
                c.*,
                COALESCE(pgd.description, '') AS column_comment
            FROM
                information_schema.columns c
                LEFT JOIN pg_catalog.pg_statio_all_tables st ON (
                    c.table_schema = st.schemaname AND
                    c.table_name = st.relname
                )
                LEFT JOIN pg_catalog.pg_description pgd ON (
                    pgd.objoid = st.relid AND
                    pgd.objsubid = c.ordinal_position
                )
            WHERE
                c.table_schema = 'public';
            '''
        
        return self.run_sql(query)
