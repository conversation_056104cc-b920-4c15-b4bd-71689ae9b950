import abc
import logging
import re
import pandas as pd
from typing import Optional, Union

from core.vanna.exception import HellodbTrainDataException
from models.datasource import Datasource
from core.hellodb.llm.qwen_chat import <PERSON>wen_<PERSON>t
from core.hellodb.vector.chromadb_vectorsotre import ChromaDB_VectorStore

class Hellodb(ChromaDB_VectorStore, Qwen_Chat):
    """
    Base class for database operations with Vanna integration.
    Each database type should have its own implementation.
    """
    
    def __init__(self, client=None, config=None):
        logging.warning('Hellodb Initialized')
        if client:
            logging.warning('Hellodb base_url: %s', client.base_url)
        if config:
            logging.warning('Hellodb model: %s', config.get('model', 'Not specified'))
            logging.warning('Hellodb path: %s', config.get('path', 'Not specified'))
        
        ChromaDB_VectorStore.__init__(self, config=config)
        Qwen_Chat.__init__(self, client=client, config=config)
    
    @abc.abstractmethod
    def connect_to_database(self, datasource: Datasource) -> None:
        """
        Connect to a database using the provided datasource information.
        
        Args:
            datasource (Datasource): The datasource object containing connection information
        
        Returns:
            None
        
        Raises:
            Exception: If connection fails
        """
        pass
    
    @abc.abstractmethod
    def get_information_schema(self, database_name: Optional[str] = None) -> pd.DataFrame:
        """
        Retrieve the information schema for the connected database.
        
        Args:
            database_name (Optional[str]): The name of the database to get schema for.
                                          If None, uses the currently connected database.
        
        Returns:
            pd.DataFrame: DataFrame containing the information schema

            contains database | table_catalog、table_schema、 table_name、column_name、data_type、comment
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        pass
    

    def get_database_summary(self, database_name: Optional[str] = None) -> str:
        """
        Retrieve the database summary for the connected database.
        
        Args:
            database_name (Optional[str]): The name of the database to get summary for
                                          If None, uses the currently connected database.
        
        Returns:
            str: The database summary,such as:
            tableA:id、title(商品标题)、price(价格)、total(数量)、create_time(下单日期)
            tableB:id、name(用户名)、phone(手机号)、email(邮箱)、create_time(注册日期)
            
        
        Raises:
            Exception: If not connected to a database or if retrieval fails
        """
        df = self.get_information_schema(database_name)

        if df.empty:
            return None
        
        # 判断是否有table_name列
        if 'table_name' not in df.columns:
            raise HellodbTrainDataException('table_name column not found in information schema')
        
        # 按表名分组处理数据
        result = []
        for table_name, group in df.groupby('table_name'):
            # 处理每一列的信息
            columns = []
            for _, row in group.iterrows():
                column_name = row['column_name']
                comment = row.get('column_comment', '')  # 使用get避免comment列不存在的情况
                
                # 如果有注释，添加括号说明，否则只使用列名
                if comment and not pd.isna(comment):
                    columns.append(f"{column_name}({comment})")
                else:
                    columns.append(column_name)
            
            # 将该表的所有列用顿号连接，并添加表名前缀
            table_summary = f"table {table_name}: {('、'.join(columns))}"
            result.append(table_summary)
        
        # 用分号和换行符连接所有表的信息
        return ';\n'.join(result)


    def update_training_data(self, id: str, question: str, sql: str, documentation: str, ddl: str) -> None:
        """
        Update training data with the provided question and SQL.
        
        Args:
            id (str): The ID of the training data to update
            question (str): The question to update
            sql (str): The SQL to update
            documentation (str): The documentation to update
            ddl (str): The DDL to update
        
        Returns:
            None
        
        Raises:
            Exception: If update fails
        """

        if id:
            self.remove_training_data(id)

        self.train(question, sql, ddl, documentation, None)


    def generate_echarts_code(self, question: str, sql: str, df: str, **kwargs) -> str:
        """
        Generate Echarts code from the provided question, SQL, and dataframe metadata.
        
        Args:
            question (str): The question to generate Echarts code for
            sql (str): The SQL query to generate Echarts code for
            df_metadata (str): The dataframe metadata to generate Echarts code for
            **kwargs: Additional keyword arguments to pass to the generation method
        
        Returns:
            str: The generated Echarts code
        
        Raises:
            Exception: If generation fails

        """
        if question is not None:
            system_msg = f"The following is a pandas DataFrame that contains the results of the query that answers the question the user asked: '{question}'"
        else:
            system_msg = "The following is a pandas DataFrame "

        if sql is not None:
            system_msg += f"\n\nThe DataFrame was produced using this query: {sql}\n\n"

        system_msg += f"The following is data 'df': \n{df}"

        message_log = [
            self.system_message(system_msg),
            self.user_message(
                "Can you generate the echarts option to chart the results of the dataframe?  If there is only one value in the df, use an Indicator. option code should include the data you can get from df. Respond with only json code. Do not answer with any explanations -- just the code."
            ),
        ]

        echarts_code = self.submit_prompt(message_log, kwargs=kwargs)

        return self._extract_json_code(echarts_code)
    

    def _extract_json_code(self, markdown_string: str) -> str:
        # Strip whitespace to avoid indentation errors in LLM-generated code
        markdown_string = markdown_string.strip()

        # Regex pattern to match json code blocks
        pattern = r"```[\w\s]*json\n([\s\S]*?)```|```([\s\S]*?)```|json\n([\s\S]*?)"

        # Find all matches in the markdown string
        matches = re.findall(pattern, markdown_string, re.IGNORECASE)

        # Extract the Python code from the matches
        json_code = []
        for match in matches:
            javascript = match[0] if match[0] else match[1]
            json_code.append(javascript.strip())

        if len(json_code) == 0:
            return markdown_string

        return json_code[0]

    def _extract_javascript_code(self, markdown_string: str) -> str:
        # Strip whitespace to avoid indentation errors in LLM-generated code
        markdown_string = markdown_string.strip()

        # Regex pattern to match Javascript code blocks
        pattern = r"```[\w\s]*javascript\n([\s\S]*?)```|```([\s\S]*?)```"

        # Find all matches in the markdown string
        matches = re.findall(pattern, markdown_string, re.IGNORECASE)

        # Extract the Python code from the matches
        javascript_code = []
        for match in matches:
            javascript = match[0] if match[0] else match[1]
            javascript_code.append(javascript.strip())

        if len(javascript_code) == 0:
            return markdown_string

        return javascript_code[0]