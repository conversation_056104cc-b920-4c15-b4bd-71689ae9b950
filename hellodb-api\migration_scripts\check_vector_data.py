#!/usr/bin/env python3
"""
检查现有向量数据存在性和数据量
"""
import os
import sys
import json
import chromadb
from chromadb.config import Settings
from datetime import datetime

def check_chroma_data(path):
    """检查指定路径的ChromaDB数据"""
    if not os.path.exists(path):
        return None
    
    try:
        client = chromadb.PersistentClient(
            path=path, 
            settings=Settings(anonymized_telemetry=False)
        )
        
        collections = client.list_collections()
        collection_info = {}
        
        for collection in collections:
            try:
                coll = client.get_collection(collection.name)
                count = coll.count()
                collection_info[collection.name] = count
            except Exception as e:
                collection_info[collection.name] = f"Error: {str(e)}"
        
        return collection_info
    except Exception as e:
        return f"Error accessing ChromaDB: {str(e)}"

def scan_vector_directories():
    """扫描向量数据目录"""
    base_paths = ['/data/chroma', '/data/hellodb/chroma']
    results = {}
    
    for base_path in base_paths:
        print(f"\n🔍 扫描目录: {base_path}")
        results[base_path] = {}
        
        if not os.path.exists(base_path):
            print(f"  ❌ 目录不存在: {base_path}")
            continue
        
        # 列出所有子目录
        try:
            subdirs = [d for d in os.listdir(base_path) 
                      if os.path.isdir(os.path.join(base_path, d))]
            
            print(f"  📁 发现 {len(subdirs)} 个子目录")
            
            for subdir in subdirs:
                full_path = os.path.join(base_path, subdir)
                print(f"    检查: {subdir}")
                
                data_info = check_chroma_data(full_path)
                results[base_path][subdir] = {
                    'path': full_path,
                    'data': data_info,
                    'checked_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                if data_info:
                    if isinstance(data_info, dict):
                        total_docs = sum(count for count in data_info.values() 
                                       if isinstance(count, int))
                        print(f"      ✅ 总文档数: {total_docs}")
                        for coll_name, count in data_info.items():
                            print(f"        {coll_name}: {count}")
                    else:
                        print(f"      ❌ {data_info}")
                else:
                    print(f"      ⚠️  无数据或无法访问")
                    
        except Exception as e:
            print(f"  ❌ 扫描目录失败: {str(e)}")
            results[base_path] = f"Error: {str(e)}"
    
    return results

def save_scan_results(results):
    """保存扫描结果"""
    output_dir = 'migration_scripts/output'
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = f'{output_dir}/vector_data_scan_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 扫描结果已保存到: {output_file}")
    return output_file

def print_summary(results):
    """打印摘要信息"""
    print(f"\n📊 向量数据扫描摘要:")
    
    total_apps = 0
    total_docs = 0
    
    for base_path, apps in results.items():
        if isinstance(apps, dict):
            app_count = len(apps)
            total_apps += app_count
            
            path_docs = 0
            for app_name, app_info in apps.items():
                if isinstance(app_info.get('data'), dict):
                    app_docs = sum(count for count in app_info['data'].values() 
                                 if isinstance(count, int))
                    path_docs += app_docs
            
            total_docs += path_docs
            print(f"  {base_path}: {app_count} 个应用, {path_docs} 个文档")
    
    print(f"\n总计: {total_apps} 个应用, {total_docs} 个文档")

if __name__ == "__main__":
    try:
        print("🔍 开始扫描向量数据...")
        results = scan_vector_directories()
        
        print_summary(results)
        
        output_file = save_scan_results(results)
        
        print(f"\n✅ 扫描完成")
        
    except Exception as e:
        print(f"❌ 扫描失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
