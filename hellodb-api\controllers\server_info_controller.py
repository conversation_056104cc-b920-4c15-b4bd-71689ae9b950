import platform
import socket

import psutil

from controllers import api
from controllers.api_response import ApiResponse
from controllers.wraps import Resource
from services.license_service import LicenseService


class ServerInfoApi(Resource):

    def platform_info(self):
        info = {
            'system': platform.system(),
            'release': platform.release(),
            'architecture': platform.machine(),
        }
        return info
    
    def network_info(self):
        hostname = socket.gethostname()
        ip_address = socket.gethostbyname(hostname)
        return {'hostname': hostname, 'ip': ip_address}
    
    def disk_info(self):
        partitions = psutil.disk_partitions(all=False)  # 获取磁盘分区信息，all=False只获取已挂载的分区
        disk_info_list = []
        for partition in partitions:
            usage = psutil.disk_usage(partition.mountpoint)
            disk_info = {
                'device': partition.device,  # 分区设备名
                'mountpoint': partition.mountpoint,  # 挂载点
                'fstype': partition.fstype,  # 文件系统类型
                'total': usage.total,  # 总大小（字节）
                'used': usage.used,  # 已使用大小（字节）
                'free': usage.free,  # 可用空间（字节）
                'percent': usage.percent,  # 占用百分比
            }
            disk_info_list.append(disk_info)
        return disk_info_list

    def post(self):
        
        return {'platform': self.platform_info(), 
                'network': self.network_info(), 
                'disk': self.disk_info() 
                }
    

class ServerPerformanceApi(Resource):

    def post(self):
        cpu_percent = psutil.cpu_percent(interval=1)  # 获取CPU使用率，interval是采样时间（秒）
        memory = psutil.virtual_memory()
        mem_info = {
            'total': memory.total,
            'used': memory.used,
            'percent': memory.percent,
        }
        return {'cpu': cpu_percent, 'memory': mem_info}


class LicenseInfoApi(Resource):

    def get(self):
        """获取系统license信息"""
        from services.license_service import LicenseService
        
        license_data = LicenseService.get_license_info()
        return ApiResponse.success(data=license_data)


class LicenseUploadApi(Resource):
    
    def post(self):
        """上传并更新license文件"""
        import json
        import os
        import datetime
        import tempfile
        from flask import request
        from cryptography.hazmat.primitives import serialization, hashes
        from cryptography.hazmat.primitives.asymmetric import padding
        from cryptography.hazmat.backends import default_backend
        # 检查是否有文件上传
        if 'file' not in request.files:
            return ApiResponse.error(msg="未找到上传的文件")
        
        license_file = request.files['file']
        if not license_file.filename.endswith('.lic'):
            return ApiResponse.error(msg="无效的文件类型，请上传.lic文件")
        
        # 创建临时文件保存上传的license
        with tempfile.NamedTemporaryFile(delete=False, mode='wb') as temp_file:
            license_file.save(temp_file)
            temp_path = temp_file.name
        
        try:
            # 从app.py复制的公钥
            PUBLIC_KEY_PEM = b'''
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr+s5ouBz0clqyyuaFrpf
cAH5goiK648HvknCG5oeIPGdM3lbYbimnPGUBDPC54MY+tUkrdZH7Gy/iJ0+0rEN
GQ7UbOwldxqKGZQWY/YT1S4KF1ipMBDQC9TXUi+vUaqUC/LIotQgUi7G8ZasQThh
3Gavinq3/qsBYM5P8UaUIX4375C+3Gmoq97FcBgfB81oBIkrMzBiIFoJbll4vlc2
i0AbjchpQosjfOCQ95lVTI2XRjrAUQZ73I57QhsS/8R3tEvnvlaEcQCZTwG1wXjI
ZnlNtwVy8dqyjTH28WLcMDkCOGRqrMeUeKeI64aWJ9i8UV8jsFPyD57a4In07KmU
HwIDAQAB
-----END PUBLIC KEY-----
'''
            
            def load_public_key():
                return serialization.load_pem_public_key(PUBLIC_KEY_PEM, backend=default_backend())
            
            # 验证license文件
            with open(temp_path, "r") as f:
                license_package = json.load(f)
            
            license_data = license_package["data"]
            signature = bytes.fromhex(license_package["signature"])
            license_json = json.dumps(license_data, sort_keys=True)
            
            # 验证签名
            public_key = load_public_key()
            public_key.verify(
                signature,
                license_json.encode(),
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256()
            )
            
            # 检查有效期
            expiry_date = datetime.datetime.strptime(license_data["expiry_date"], "%Y-%m-%d")
            if expiry_date < datetime.datetime.now():
                raise ValueError("License 已过期")
            
            # 更新License信息到常量类
            LicenseService.update_license_info(
                customer=license_data["customer"],
                expiry_date=license_data["expiry_date"],
                domain=license_data.get("domain", ""),
                license_code=license_data.get("license_code", "")
            )
            
            # 将新的license文件复制到正确的位置
            import shutil
            shutil.copy2(temp_path, "license.lic")
            
            return ApiResponse.success(msg="License更新成功", data=LicenseService.get_license_info())
        except ValueError as e:
            return ApiResponse.error(msg=f"License验证失败: {str(e)}")
        except Exception as e:
            return ApiResponse.error(msg=f"处理License文件时出错: {str(e)}")
        finally:
            # 清理临时文件
            os.unlink(temp_path)
 


api.add_resource(ServerInfoApi, '/server/info')
api.add_resource(ServerPerformanceApi, '/server/performance')
api.add_resource(LicenseInfoApi, '/server/license/info')
api.add_resource(LicenseUploadApi, '/server/license/upload')