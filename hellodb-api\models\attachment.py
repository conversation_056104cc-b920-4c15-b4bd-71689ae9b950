from enum import Enum
import json
from sqlalchemy.dialects.postgresql import JSONB, UUID
from extensions.ext_database import db
from models.account import Account
from datetime import datetime

# 定义枚举
class NamespaceEnum(Enum):
    KNOWNLEDGE = 'knownledge'
    CHAT = 'chat'
    STATIC = 'static'
    DEFAULT = ''

class FileTypeEnum(Enum):
    IMAGE = 'image'
    OFFICE = 'office'
    # 添加其他类型...

# 定义上传附件模型
class UploadAttachment(db.Model):
    __tablename__ = 'upload_attachments'
    __table_args__ = (
        db.PrimaryKeyConstraint('id', name='upload_attachment_pkey'),
    )

    id = db.Column(UUID, server_default=db.text('uuid_generate_v4()'), nullable=False)
    ns = db.Column(db.String(255), nullable=False)  # 命名空间
    original_name = db.Column(db.String(255), nullable=False)  # 原始文件名
    filename = db.Column(db.String(255), nullable=False)  # 保存的文件名
    local_path = db.Column(db.String(255), nullable=False)  # 本地路径
    url_path = db.Column(db.String(255), nullable=False)  # 访问路径
    type = db.Column(db.String(50), nullable=False)  # 文件类型
    created_by = db.Column(UUID, nullable=False)  # 创建者ID
    created_at = db.Column(db.DateTime, nullable=False, server_default=db.text('CURRENT_TIMESTAMP(0)'))  # 创建时间
    updated_by = db.Column(UUID, nullable=True)  # 更新者ID
    updated_at = db.Column(db.DateTime, nullable=False, server_default=db.text('CURRENT_TIMESTAMP(0)'))  # 更新时间

    @property
    def is_image(self):
        return self.type in ['image', 'png', 'jpg', 'jpeg', 'gif']

    @property
    def is_office(self):
        return self.type in ['office', 'doc', 'docx', 'xls', 'xlsx']
