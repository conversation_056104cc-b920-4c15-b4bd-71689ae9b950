from flask_restful import fields
from fields.custom_fields import CustomDateTime

datasource_fields = {
    'id': fields.String,
    'name': fields.String,
    'type': fields.String,
    'host': fields.String,
    'port': fields.Integer,
    'database': fields.String,
    'username': fields.String,
    'ssl': fields.Boolean,
    'dsn': fields.String,
    'tds_version': fields.String,
    'created_at': CustomDateTime,
    'updated_at': CustomDateTime
}

datasource_list_fields = {
    'code': fields.Integer,
    'msg': fields.String,
    'data': fields.Nested({
        'datasources': fields.List(fields.Nested(datasource_fields)),
        'total': fields.Integer
    })
}

single_datasource_fields = {
    'code': fields.Integer,
    'msg': fields.String,
    'data': fields.Nested(datasource_fields)
}
