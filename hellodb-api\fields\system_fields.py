from flask_restful import fields
from fields.custom_fields import CustomDateTime

# 基础字段
base_fields = {
    'id': fields.String,
    'status': fields.String,
    'created_at': CustomDateTime,
    'updated_at': CustomDateTime
}

# 用户管理字段
user_fields = {
    'id': fields.String,  # 因为使用了 UUID，但可以用字符串来表示  
    'name': fields.String,  
    'email': fields.String,
    'email_verified_at': CustomDateTime,
    'password': fields.String(attribute=None),  # 如果不想在 API 中暴露密码，可以设置 attribute=None  
    'password_salt': fields.String(attribute=None),  # 同样，如果不想在 API 中暴露 salt
    'mobile': fields.String,
    'avatar': fields.String,
    'region': fields.String,
    'interface_language': fields.String,  
    'interface_theme': fields.String,  
    'timezone': fields.String,  
    'last_login_at': CustomDateTime,  # 如果需要特定的日期时间格式  
    'last_login_ip': fields.String,  
    'last_active_at': CustomDateTime, 
    'status': fields.String,  
    'initialized_at': CustomDateTime, 
    'created_at': CustomDateTime,
    'updated_at': CustomDateTime,
    'roles': fields.List(fields.String),
}

# 角色管理字段
role_fields = {
    **base_fields,
    'name': fields.String,
    'code': fields.String,
    'description': fields.String
}

# 权限字段
permission_fields = {
    **base_fields,
    'name': fields.String,
    'code': fields.String,
    'type': fields.String
}

# 菜单管理字段
menu_fields = {
    'id': fields.String,
    'name': fields.String,
    'path': fields.String,
    'icon': fields.String,
    'sort': fields.Integer,
    'type': fields.String,
    'permission': fields.String,
    'parent_id': fields.String,
    'status': fields.String,
}

# 部门管理字段
department_fields = {
    **base_fields,
    'name': fields.String,
    'code': fields.String,
    'leader': fields.String,
    'phone': fields.String,
    'email': fields.String,
    'sort': fields.Integer,
    'parent_id': fields.String,
}

# 分页响应字段
page_response_fields = {
    'code': fields.Integer,
    'msg': fields.String,
    'data': fields.Nested({
        'list': fields.List(fields.Raw),
        'total': fields.Integer
    })
}

# 通用响应字段
common_response_fields = {
    'code': fields.Integer,
    'msg': fields.String,
    'data': fields.Raw
}

# 用户Profile响应字段
user_profile_fields = {
    'user': fields.Nested(user_fields),
    'recent_apps': fields.List(fields.Nested({
        'id': fields.String,
        'name': fields.String,
        'description': fields.String,
        'created_at': CustomDateTime,
    })),
    'recent_datasources': fields.List(fields.Nested({
        'id': fields.String,
        'name': fields.String,
        'type': fields.String,
        'created_at': CustomDateTime,
    })),
    'recent_logs': fields.List(fields.Nested({
        'id': fields.String,
        'method': fields.String,
        'path': fields.String,
        'status_code': fields.Integer,
        'created_at': CustomDateTime
    })),
    'recent_messages': fields.List(fields.Nested({
        'id': fields.String,
        'question': fields.String,
        'answer_sql_valid': fields.Boolean,
        'answer_data_row_count': fields.Integer,
        'created_at': CustomDateTime
    }))
}

user_profile_response_fields = {
    'code': fields.Integer,
    'msg': fields.String,
    'data': fields.Nested(user_profile_fields)
}