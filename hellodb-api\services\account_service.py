import base64
import logging
import secrets
import uuid
from datetime import datetime, timed<PERSON>ta
from hashlib import sha256
from typing import Any, Optional

from flask import current_app
from sqlalchemy import func
from werkzeug.exceptions import Unauthorized

from libs.helper import get_remote_ip
from libs.passport import PassportService
from libs.password import compare_password, hash_password, valid_password
from models.account import *
from services.errors.account import (
    AccountLoginError,
    AccountRegisterError,
    CannotOperateSelfError,
    CurrentPasswordIncorrectError,
    InvalidActionError,
    LinkAccountIntegrateError,
    NoPermissionError,
    RoleAlreadyAssignedError,
)


class AccountService:

    @staticmethod
    def load_account_by_username(user_name: str) -> Account:
        if user_name:
            account = Account.query.filter_by(name=user_name.lower()).first()
        else:
            raise Exception("请指定用户名")
        return account


    @staticmethod
    def load_account_by_mobile(mobile: str) -> Account:
        if mobile:
            account = Account.query.filter_by(mobile=mobile).first()
        else:
            raise Exception("请指定手机号")
        return account


    @staticmethod
    def load_account(user_id: str) -> Account:
        account = Account.query.filter_by(id=user_id).first()
        return account


    @staticmethod
    def load_user(user_id: str) -> Account:
        account = Account.query.filter_by(id=user_id).first()
        if not account:
            return None

        if account.status in [AccountStatus.BANNED.value, AccountStatus.CLOSED.value]:
            raise Unauthorized("Account is banned or closed.")

        if datetime.now().replace(tzinfo=None) - account.last_active_at > timedelta(minutes=10):
            account.last_active_at = datetime.now().replace(tzinfo=None)
            db.session.commit()

        return account


    @staticmethod
    def get_account_jwt_token(account):
        payload = {
            "user_id": account.id,
            "exp": datetime.now().replace(tzinfo=None) + timedelta(days=30),
            "iss": current_app.config['EDITION'],
            "sub": 'Console API Passport',
        }

        token = PassportService().issue(payload)
        return token

    @staticmethod
    def authenticate(email: str, password: str) -> Account:
        """authenticate account with email and password"""

        account = Account.query.filter_by(email=email).first()
        if not account:
            raise AccountLoginError('Invalid email or password.')

        if account.status == AccountStatus.BANNED.value or account.status == AccountStatus.CLOSED.value:
            raise AccountLoginError('Account is banned or closed.')

        if account.status == AccountStatus.PENDING.value:
            account.status = AccountStatus.ACTIVE.value
            account.initialized_at = datetime.now().replace(tzinfo=None)
            db.session.commit()

        if account.password is None or not compare_password(password, account.password, account.password_salt):
            raise AccountLoginError('Invalid email or password.')
        return account

    @staticmethod
    def update_account_password(account, password, new_password):
        """update account password"""
        if account.password and not compare_password(password, account.password, account.password_salt):
            raise CurrentPasswordIncorrectError("Current password is incorrect.")

        # may be raised
        valid_password(new_password)

        # generate password salt
        salt = secrets.token_bytes(16)
        base64_salt = base64.b64encode(salt).decode()

        # encrypt password with salt
        password_hashed = hash_password(new_password, salt)
        base64_password_hashed = base64.b64encode(password_hashed).decode()
        account.password = base64_password_hashed
        account.password_salt = base64_salt
        db.session.commit()
        return account

    @staticmethod
    def create_account(name: str, email: str = None, mobile: str = None, password: str = None,
                       interface_language: str = 'zh-CN',
                       interface_theme: str = 'light',
                       timezone: str = 'Asia/Shanghai', ) -> Account:
        """create account"""
        account = Account()
        account.email = email.lower() if email else None
        account.name = name.lower() #对用户名转换为小写存储
        account.mobile = mobile

        if password:
            # generate password salt
            salt = secrets.token_bytes(16)
            base64_salt = base64.b64encode(salt).decode()

            # encrypt password with salt
            password_hashed = hash_password(password, salt)
            base64_password_hashed = base64.b64encode(password_hashed).decode()

            account.password = base64_password_hashed
            account.password_salt = base64_salt

        account.interface_language = interface_language
        account.interface_theme = interface_theme

        # Set timezone based on language
        account.timezone = timezone

        db.session.add(account)
        db.session.commit()
        return account

    @staticmethod
    def link_account_integrate(provider: str, open_id: str, account: Account) -> None:
        """Link account integrate"""
        try:
            # Query whether there is an existing binding record for the same provider
            account_integrate: Optional[AccountIntegrate] = AccountIntegrate.query.filter_by(account_id=account.id,
                                                                                             provider=provider).first()

            if account_integrate:
                # If it exists, update the record
                account_integrate.open_id = open_id
                account_integrate.encrypted_token = ""  # todo
                account_integrate.updated_at = datetime.now().replace(tzinfo=None)
            else:
                # If it does not exist, create a new record
                account_integrate = AccountIntegrate(account_id=account.id, provider=provider, open_id=open_id,
                                                     encrypted_token="")
                db.session.add(account_integrate)

            db.session.commit()
            logging.info(f'Account {account.id} linked {provider} account {open_id}.')
        except Exception as e:
            logging.exception(f'Failed to link {provider} account {open_id} to Account {account.id}')
            raise LinkAccountIntegrateError('Failed to link account.') from e

    @staticmethod
    def close_account(account: Account) -> None:
        """Close account"""
        account.status = AccountStatus.CLOSED.value
        db.session.commit()

    @staticmethod
    def ban_account(id: str) -> None:
        """Ban account"""
        account = Account.query.filter_by(id=id).first()
        account.status = AccountStatus.BANNED.value
        db.session.commit()

    @staticmethod
    def change_account_status(id: str, status: str) -> None:
        """Change account status, 状态必须在枚举之中"""
        if status not in AccountStatus.__members__.values():
            raise ValueError("Invalid status value.")

        account = Account.query.filter_by(id=id).first()
        account.status = status
        db.session.commit()

    @staticmethod
    def update_account(account, **kwargs):
        """
        Update account fields
        注意使用这个方法时，需要对name转换为小写
        """

        # 检查用户名是否已存在，需要对用户名转换成小写
        if 'name' in kwargs and kwargs['name'] != account.name:
            kwargs['name'] = kwargs['name'].lower()
            if Account.query.filter_by(name=kwargs['name']).first():
                raise Exception('用户名已存在')
            
        # 检查邮箱是否已存在，需要对邮箱转换成小写
        if 'email' in kwargs and kwargs['email'] != account.email:
            kwargs['email'] = kwargs['email'].lower()
            if Account.query.filter_by(email=kwargs['email']).first():
                raise Exception('邮箱已存在')
            
        # 检查手机号是否已存在
        if 'mobile' in kwargs and kwargs['mobile'] != account.mobile:
            if Account.query.filter_by(mobile=kwargs['mobile']).first():
                raise Exception('手机号已存在')

        for field, value in kwargs.items():
            if hasattr(account, field):
                setattr(account, field, value)
            else:
                raise AttributeError(f"Invalid field: {field}")

        db.session.commit()
        return account

    @staticmethod
    def update_last_login(account: Account, request) -> None:
        """Update last login time and ip"""
        account.last_login_at = datetime.now().replace(tzinfo=None)
        account.last_login_ip = get_remote_ip(request)
        db.session.add(account)
        db.session.commit()
        logging.info(f'Account {account.id} logged in successfully.')


    @staticmethod
    def update_last_active(account: Account) -> None:
        account.last_active_at = datetime.now().replace(tzinfo=None)
        db.session.add(account)
        db.session.commit()

    @staticmethod
    def list_accounts_by_page(page: int, page_size: int):
        """List accounts by page"""
        return Account.query.order_by(Account.created_at.desc()).paginate(page=page, per_page=page_size, error_out=False)
    

        
    @staticmethod
    def get_user_roles(account: Account):
        
        if not account:
            return []
        elif account.name == 'maa7' or account.name == 'guoweiwei' or account.name == 'admin' or account.name == '***********':
            return ['USER', 'ADMIN']
        else:
            return ['USER']
        
        
    @staticmethod
    def has_role(account: Account, role: str) -> bool:
        roles = AccountService.get_user_roles(account)
        return role in roles

    @staticmethod
    def get_account_profile(account_id: str):
        """Get account profile with recent activities"""
        from models.datasource import DataSourceApp, Datasource, ChatdbMessage
        from models.http_request_log import HttpRequestLog
        
        account = Account.query.get(account_id)
        if not account:
            return None
            
        # Get recent 5 apps
        recent_apps = DataSourceApp.query.filter_by(
            created_by=account_id
        ).order_by(
            DataSourceApp.created_at.desc()
        ).limit(5).all()

        # Get recent 5 datasources
        recent_datasources = Datasource.query.filter_by(
            created_by=account_id
        ).order_by(
            Datasource.created_at.desc()
        ).limit(5).all()

        # Get recent 5 logs
        recent_logs = HttpRequestLog.query.filter_by(
            account_id=account_id
        ).order_by(
            HttpRequestLog.created_at.desc()
        ).limit(5).all()

        # Get recent 5 messages
        recent_messages = ChatdbMessage.query.filter_by(
            created_by=account_id
        ).order_by(
            ChatdbMessage.created_at.desc()
        ).limit(5).all()

        return {
            'account': account,
            'recent_apps': recent_apps,
            'recent_datasources': recent_datasources,
            'recent_logs': recent_logs,
            'recent_messages': recent_messages
        }

    @staticmethod
    def delete_account(account_id: str) -> None:
        """Soft delete account by setting status to deleted"""
        account = Account.query.get(account_id)
        if account:
            account.status = AccountStatus.DELETED.value
            db.session.commit()

    @staticmethod
    def check_name_exists(name: str, exclude_id: str = None) -> bool:
        """Check if username already exists"""
        query = Account.query.filter(Account.name == name.lower())
        if exclude_id:
            query = query.filter(Account.id != exclude_id)
        return query.first() is not None

    @staticmethod
    def check_email_exists(email: str, exclude_id: str = None) -> bool:
        """Check if email already exists"""
        query = Account.query.filter(Account.email == email.lower())
        if exclude_id:
            query = query.filter(Account.id != exclude_id)
        return query.first() is not None

    @staticmethod
    def check_mobile_exists(mobile: str, exclude_id: str = None) -> bool:
        """Check if mobile already exists"""
        query = Account.query.filter(Account.mobile == mobile)
        if exclude_id:
            query = query.filter(Account.id != exclude_id)
        return query.first() is not None


class RegisterService:

    @classmethod
    def _get_invitation_token_key(cls, token: str) -> str:
        return f'member_invite:token:{token}'

    @classmethod
    def register(cls, email, name, password: str = None, open_id: str = None, provider: str = None,
                 language: str = None, status: AccountStatus = None) -> Account:
        db.session.begin_nested()
        """Register account"""
        try:
            account = AccountService.create_account(
                name=name,
                email=email,
                password=password,
                interface_language=language if language else languages[0]
            )
            account.status = AccountStatus.ACTIVE.value if not status else status.value
            account.initialized_at = datetime.now().replace(tzinfo=None)

            if open_id is not None or provider is not None:
                AccountService.link_account_integrate(provider, open_id, account)

            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logging.error(f'Register failed: {e}')
            raise AccountRegisterError(f'Registration failed: {e}') from e

        return account

    @classmethod
    def invite_new_member(cls, email: str, language: str, role: str = 'normal', inviter: Account = None) -> str:
        """Invite new member"""
        account = Account.query.filter_by(email=email).first()

        if not account:

            name = email.split('@')[0]

            account = cls.register(email=email, name=name, language=language, status=AccountStatus.PENDING)

        token = cls.generate_invite_token(account)

        # send email
        '''
        send_invite_member_mail_task.delay(
            language=account.interface_language,
            to=email,
            token=token,
            inviter_name=inviter.name if inviter else 'Dify'
        )
        '''

        return token

    @classmethod
    def generate_invite_token(cls, account: Account) -> str:
        token = str(uuid.uuid4())
        invitation_data = {
            'account_id': account.id,
            'email': account.email
        }
        expiryHours = current_app.config['INVITE_EXPIRY_HOURS']
        redis_client.setex(
            cls._get_invitation_token_key(token),
            expiryHours * 60 * 60,
            json.dumps(invitation_data)
        )
        return token

    @classmethod
    def revoke_token(cls, workspace_id: str, email: str, token: str):
        if workspace_id and email:
            email_hash = sha256(email.encode()).hexdigest()
            cache_key = 'member_invite_token:{}, {}:{}'.format(workspace_id, email_hash, token)
            redis_client.delete(cache_key)
        else:
            redis_client.delete(cls._get_invitation_token_key(token))

    @classmethod
    def get_invitation_if_token_valid(cls, workspace_id: str, email: str, token: str) -> Optional[dict[str, Any]]:
        invitation_data = cls._get_invitation_by_token(token, workspace_id, email)
        if not invitation_data:
            return None

        if invitation_data['account_id'] != str(account.id):
            return None

        return {
            'account': account,
            'data': invitation_data
        }

    @classmethod
    def _get_invitation_by_token(cls, token: str, workspace_id: str, email: str) -> Optional[dict[str, str]]:
        if workspace_id is not None and email is not None:
            email_hash = sha256(email.encode()).hexdigest()
            cache_key = f'member_invite_token:{workspace_id}, {email_hash}:{token}'
            account_id = redis_client.get(cache_key)

            if not account_id:
                return None

            return {
                'account_id': account_id.decode('utf-8'),
                'email': email,
                'workspace_id': workspace_id,
            }
        else:
            data = redis_client.get(cls._get_invitation_token_key(token))
            if not data:
                return None

            invitation = json.loads(data)
            return invitation