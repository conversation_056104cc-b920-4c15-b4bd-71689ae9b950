from typing import Optional

from core.vanna.hellodb_vanna import HellodbVanna
from core.vanna.mysql_hellodb_vanna import MySQLHellodbVanna
from core.vanna.postgresql_hellodb_vanna import PostgreSQLHellodbVanna
from core.vanna.clickhouse_hellodb_vanna import ClickHouseHellodbVanna
from core.vanna.hive_hellodb_vanna import HiveHellodbVanna
from core.vanna.oracle_hellodb_vanna import OracleHellodbVanna
from core.vanna.sqlserver_pymssql_hellodb_vanna import SQLServerPyMSSQLHellodbVanna
from models.datasource import DataSourceApp


class HellodbVannaFactory:
    """
    Factory class for creating HellodbVanna instances based on database type.
    """

    @staticmethod
    def create_vanna(datasourceApp: DataSourceApp, client=None, config=None) -> HellodbVanna:
        """
        Create and return a HellodbVanna instance based on the datasource type.

        Args:
            datasource (Datasource): The datasource object containing connection information
            client: The client to use for the HellodbVanna instance
            config: The configuration to use for the HellodbVanna instance

        Returns:
            HellodbVanna: An instance of the appropriate HellodbVanna implementation

        Raises:
            ValueError: If the database type is not supported
        """
        db_type = datasourceApp.datasource_type.lower() if datasourceApp.datasource_type else ""

        if db_type == "mysql":
            return MySQLHellodbVanna(client=client, config=config)
        elif db_type == "postgresql":
            return PostgreSQLHellodbVanna(client=client, config=config)
        elif db_type == "sqlserver":
            return SQLServerPyMSSQLHellodbVanna(client=client, config=config)
        elif db_type == "clickhouse":
            return ClickHouseHellodbVanna(client=client, config=config)
        elif db_type == "hive":
            return HiveHellodbVanna(client=client, config=config)
        elif db_type == "oracle":
            return OracleHellodbVanna(client=client, config=config)
        else:
            raise ValueError(f"Unsupported database type: {datasourceApp.datasource_type}")

    @staticmethod
    def create_and_connect(datasourceApp: DataSourceApp, client=None, config=None) -> HellodbVanna:
        """
        Create a HellodbVanna instance and connect to the database.

        Args:
            datasourceApp (DataSourceApp): The datasource app object containing connection information
            client: The client to use for the HellodbVanna instance
            config: The configuration to use for the HellodbVanna instance

        Returns:
            HellodbVanna: A connected instance of the appropriate HellodbVanna implementation

        Raises:
            ValueError: If the database type is not supported
        """
        vanna = HellodbVannaFactory.create_vanna(datasourceApp, client=client, config=config)
        vanna.connect_to_database(datasourceApp.datasource)
        return vanna