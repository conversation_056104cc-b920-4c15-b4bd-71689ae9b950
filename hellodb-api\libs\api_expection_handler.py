import re
import sys

from flask import current_app, jsonify
from flask_restful import Api, http_status_message
import psycopg2
import vanna
from werkzeug.datastructures import Headers
from werkzeug.exceptions import HTTPException

from controllers.api_response import ApiResponse
from services.errors.account import Account<PERSON>oginError

def handle_error(e):
    if isinstance(e, HTTPException):
        if e.response is not None:
            resp = e.get_response()
            return resp

        status_code = e.code
        default_data = {
            'code': re.sub(r'(?<!^)(?=[A-Z])', '_', type(e).__name__).lower(),
            'msg': getattr(e, 'description', http_status_message(status_code)),
            'status': status_code
        }

        if default_data['msg'] and default_data['msg'] == 'Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)':
            default_data['msg'] = 'Invalid JSON payload received or JSON payload is empty.'

    elif isinstance(e, ValueError):
        status_code = 200
        default_data = ApiResponse.error(msg=f'无效参数: {e.args}')
    elif isinstance(e, AccountLoginError):
        status_code = 200
        default_data = ApiResponse.error(msg=f'登录异常: {e.description}')
    elif isinstance(e, vanna.exceptions.ConnectionError):
        status_code = 200
        default_data = ApiResponse.error(msg=f'数据库连接异常: {e.args}')
    elif isinstance(e, psycopg2.errors.ForeignKeyViolation):
        status_code = 200
        default_data = ApiResponse.error(msg=f'外键约束异常: 删除时存在关联记录；或新增时关联记录不存在！')
    else:
        status_code = 500
        default_data = {
            'msg': f'HTTP STATUS:{http_status_message(status_code)}, 接口异常，请刷新当前页面，或稍后再试',
        }

    resp = current_app.make_response(jsonify(default_data))
    resp.status_code = status_code
    return resp