#!/usr/bin/env python3
"""
直接测试修复后的应用获取逻辑
"""
import os
import sys

def get_all_app_aliases_fixed():
    """修复后的获取所有应用的别名列表函数"""
    try:
        # 方法1: 直接扫描向量存储目录
        chroma_path = '/data/chroma'
        if os.path.exists(chroma_path):
            all_dirs = [d for d in os.listdir(chroma_path) 
                       if os.path.isdir(os.path.join(chroma_path, d)) 
                       and not d.startswith('.')]  # 排除隐藏目录
            
            print(f"📁 从向量存储目录扫描到 {len(all_dirs)} 个应用")
            
            # 过滤掉明显的系统目录
            filtered_apps = []
            for app in all_dirs:
                # 排除数字目录和明显的系统目录
                if app.isdigit() or app in ['.cache', 'Stop_lm']:
                    print(f"   跳过系统目录: {app}")
                    continue
                filtered_apps.append(app)
            
            print(f"📊 过滤后的应用数量: {len(filtered_apps)}")
            return sorted(filtered_apps)
        else:
            print(f"❌ 向量存储目录不存在: {chroma_path}")
            return []
            
    except Exception as e:
        print(f"❌ 扫描向量存储目录失败: {str(e)}")
        return []

def compare_with_scan_file():
    """对比扫描文件的结果"""
    scan_file = 'migration_scripts/output/vector_data_scan_results.json'
    
    if not os.path.exists(scan_file):
        print(f"❌ 扫描文件不存在: {scan_file}")
        return []
    
    try:
        import json
        with open(scan_file, 'r', encoding='utf-8') as f:
            scan_data = json.load(f)
        
        scan_apps = list(scan_data.get('/data/chroma', {}).keys())
        print(f"📁 扫描文件中的应用数量: {len(scan_apps)}")
        return sorted(scan_apps)
        
    except Exception as e:
        print(f"❌ 读取扫描文件失败: {str(e)}")
        return []

def main():
    """主函数"""
    print("🔍 直接测试修复后的应用获取逻辑")
    print("=" * 50)
    
    # 测试修复后的函数
    print("\n1. 测试修复后的目录扫描:")
    fixed_apps = get_all_app_aliases_fixed()
    
    # 对比扫描文件
    print("\n2. 对比原扫描文件:")
    scan_apps = compare_with_scan_file()
    
    # 分析差异
    print("\n3. 差异分析:")
    if fixed_apps and scan_apps:
        fixed_set = set(fixed_apps)
        scan_set = set(scan_apps)
        
        print(f"   目录扫描: {len(fixed_apps)} 个应用")
        print(f"   扫描文件: {len(scan_apps)} 个应用")
        
        # 检查kehu-clickhouse
        print(f"\n   kehu-clickhouse 状态:")
        print(f"   - 目录扫描: {'✅ 存在' if 'kehu-clickhouse' in fixed_set else '❌ 不存在'}")
        print(f"   - 扫描文件: {'✅ 存在' if 'kehu-clickhouse' in scan_set else '❌ 不存在'}")
        
        # 找出差异
        missing_in_scan = fixed_set - scan_set
        extra_in_scan = scan_set - fixed_set
        
        if missing_in_scan:
            print(f"\n   📋 目录中有但扫描文件中缺失的应用 ({len(missing_in_scan)} 个):")
            for i, app in enumerate(sorted(list(missing_in_scan))[:10]):
                marker = " ⭐" if app == 'kehu-clickhouse' else ""
                print(f"      {i+1:2d}. {app}{marker}")
            if len(missing_in_scan) > 10:
                print(f"      ... 还有 {len(missing_in_scan) - 10} 个")
        
        if extra_in_scan:
            print(f"\n   📋 扫描文件中有但目录中缺失的应用 ({len(extra_in_scan)} 个):")
            for i, app in enumerate(sorted(list(extra_in_scan))[:10]):
                print(f"      {i+1:2d}. {app}")
            if len(extra_in_scan) > 10:
                print(f"      ... 还有 {len(extra_in_scan) - 10} 个")
    
    print("\n4. 结论:")
    if 'kehu-clickhouse' in fixed_apps:
        print("   ✅ 修复后的逻辑可以正确找到 kehu-clickhouse")
        print("   ✅ 问题确实是扫描文件不完整导致的")
        print("   📋 建议: 使用修复后的逻辑重新运行完整迁移")
    else:
        print("   ❌ 修复后的逻辑仍然找不到 kehu-clickhouse")
        print("   📋 需要进一步调查目录结构或应用名称")
    
    print(f"\n5. 下一步行动:")
    if len(fixed_apps) > len(scan_apps):
        print(f"   1. 修复后可以处理 {len(fixed_apps)} 个应用（比原来多 {len(fixed_apps) - len(scan_apps)} 个）")
        print(f"   2. 重新运行迁移应该能包含 kehu-clickhouse")
        print(f"   3. 执行命令: python migration_scripts/full_migration.py")
    else:
        print(f"   1. 修复效果不明显，需要进一步调查")
        print(f"   2. 检查目录权限和路径配置")

if __name__ == "__main__":
    main()
