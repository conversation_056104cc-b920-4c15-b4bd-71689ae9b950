"""empty message

Revision ID: 05da4b38be21
Revises: 8d212d7230d7
Create Date: 2025-06-16 23:30:36.365276

"""
from alembic import op
import sqlalchemy as sa
import sqlalchemy_utils  # 自动添加的依赖导入


# revision identifiers, used by Alembic.
revision = '05da4b38be21'
down_revision = '8d212d7230d7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasources', schema=None) as batch_op:
        batch_op.alter_column('host',
               existing_type=sa.VARCHAR(length=255),
               nullable=True,
               existing_comment='数据源主机地址')
        batch_op.alter_column('port',
               existing_type=sa.INTEGER(),
               nullable=True,
               existing_comment='数据源端口')
        batch_op.alter_column('database',
               existing_type=sa.VARCHAR(length=255),
               nullable=True,
               existing_comment='数据源数据库名称')
        batch_op.add_column(sa.Column('dsn', sa.String(length=255), nullable=True, comment='数据源DSN'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasources', schema=None) as batch_op:
        batch_op.alter_column('database',
               existing_type=sa.VARCHAR(length=255),
               nullable=False,
               existing_comment='数据源数据库名称')
        batch_op.alter_column('port',
               existing_type=sa.INTEGER(),
               nullable=False,
               existing_comment='数据源端口')
        batch_op.alter_column('host',
               existing_type=sa.VARCHAR(length=255),
               nullable=False,
               existing_comment='数据源主机地址')
        batch_op.drop_column('dsn')

    # ### end Alembic commands ###
