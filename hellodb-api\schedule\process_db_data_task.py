from xinference_client import RESTful<PERSON>lient as Client
from flask import current_app as app
def process_db_data_task():
    pass


def embembedding_text(text: str):
    client_url = app.config.get("XINFERENCE_URL")
    model_name = app.config.get("XINFERENCE_EMBEDDING_MODEL_NAME")
    client = Client(client_url)
    # The bge-small-en-v1.5 is an embedding model, so the `model_type` needs to be specified.
    model_uid = client.launch_model(model_name=model_name, model_type="embedding")
    model = client.get_model(model_uid)
    embedding = model.create_embedding(text)

    return embedding.embedding
