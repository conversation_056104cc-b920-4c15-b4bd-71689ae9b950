from flask import request
from flask_login import login_required
from flask_restful import marshal_with

from controllers.system import api
from controllers.wraps import WebApiResource
from controllers.api_response import ApiResponse
from fields.system_fields import user_fields, page_response_fields, common_response_fields
from models.system import User
from extensions.ext_database import db
from models.http_request_log import HttpRequestLog

class ApiLogList(WebApiResource):

    @login_required
    def get(self):

        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        account_id = request.args.get('account_id')
        account_id = account_id.strip() if account_id else None

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        method = request.args.get('method')
        path = request.args.get('path')
        ip_address = request.args.get('ip_address')
        status_code = request.args.get('status_code')
        filter_error_response_data = request.args.get('filter_error_response_data', default=False)

        query = HttpRequestLog.query

        if start_date and end_date:
            query = query.filter(HttpRequestLog.created_at >= start_date, HttpRequestLog.created_at <= end_date)
        if method:
            query = query.filter(HttpRequestLog.method == method)
        if path:
            # path 用like查询
            query = query.filter(HttpRequestLog.path.like(f'{path}%'))
            # query = query.filter(HttpRequestLog.path == path)
        if ip_address:
            query = query.filter(HttpRequestLog.ip_address == ip_address)

        if account_id:
            query = query.filter(HttpRequestLog.account_id == account_id)

        if status_code:
            query = query.filter(HttpRequestLog.status_code == status_code)

        if filter_error_response_data == 'true':
            query = query.filter(HttpRequestLog.response_error_data != '')

        logs = query.order_by(HttpRequestLog.created_at.desc()).paginate(page=page, per_page=page_size, error_out=False)

        return ApiResponse.paginated_from_pagination(logs)

api.add_resource(ApiLogList, '/api/system/logs')