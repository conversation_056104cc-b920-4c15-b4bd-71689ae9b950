from typing import Dict
import pypandoc
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
def markdown_to_word_with_template(markdown_text, output_filename, template_path):
    # 使用自定义模板将Markdown转换为Word文档  https://pandoc.org/MANUAL.html
    pypandoc.convert_text(
        markdown_text,
        'docx',
        format='md',
        outputfile=output_filename,
        extra_args=['--reference-doc', template_path, 
                    '--toc', '--toc-depth=3', 
                    '--lua-filter', '/app/api/libs/pandoc-ext/pagebreak.lua',
                    '--variable', 'toc-title=目录']
    )


def markdown_to_word_with_template_without_toc(markdown_text, output_filename, template_path):
    # 使用自定义模板将Markdown转换为Word文档  https://pandoc.org/MANUAL.html
    pypandoc.convert_text(
        markdown_text,
        'docx',
        format='md',
        outputfile=output_filename,
        extra_args=['--reference-doc', template_path]
    )


def replace_placeholder_in_header_footer_for_doc(doc: Document, replacements: Dict[str, str]):
    # 遍历文档中的页眉
    for section in doc.sections:
        # 页眉
        for header in section.header.paragraphs:
            for key, value in replacements.items():
                if key in header.text:
                    inline_shapes = header.runs
                    for run in inline_shapes:
                        if key in run.text:
                            run.text = run.text.replace(key, value)
        
        # 页脚
        for footer in section.footer.paragraphs:
            for key, value in replacements.items():
                if key in footer.text:
                    inline_shapes = footer.runs
                    for run in inline_shapes:
                        if key in run.text:
                            run.text = run.text.replace(key, value)


def replace_placeholder_in_header_footer(file_path, replacements: Dict[str, str]):
    # 打开文档
    doc = Document(file_path)
    
    # 替换页眉和页脚中的占位符
    replace_placeholder_in_header_footer_for_doc(doc, replacements)
    
    # 保存文档（覆盖原文件）
    doc.save(file_path)

def replace_placeholder_in_docx(file_path, replacements: Dict[str, str]):
    # 打开文档
    doc = Document(file_path)
    
    # 替换页眉和页脚中的占位符
    replace_placeholder_in_header_footer_for_doc(doc, replacements)
    
    # 替换文档主体中的占位符
    for paragraph in doc.paragraphs:
        for key, value in replacements.items():
            if key in paragraph.text:
                inline_shapes = paragraph.runs
                for run in inline_shapes:
                    if key in run.text:
                        run.text = run.text.replace(key, value)
    
    # 替换表格中的占位符
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for key, value in replacements.items():
                    if key in cell.text:
                        inline_shapes = cell.paragraphs
                        for paragraph in inline_shapes:
                            if key in paragraph.text:
                                inline_shapes = paragraph.runs
                                for run in inline_shapes:
                                    if key in run.text:
                                        run.text = run.text.replace(key, value)
    
    # 保存文档（覆盖原文件）
    doc.save(file_path)


def process_docx(file_path, replacements: Dict[str, str]):
    # 读取Word文档
    return replace_placeholder_in_header_footer(file_path, replacements)
    
    


# 写个main方法
if __name__ == '__main__':
    # 读取Markdown文件
    markdown_text = """
# 用户管理模块

## 1. 模块概述
用户管理模块是桌面云迁移工具中负责管理用户信息、权限和角色的核心部分。该模块提供用户注册与登录、用户角色与权限管理以及用户信息编辑与维护的功能，以确保系统的安全性和可控性。

### 6. 安全性要求
- 报告与分析模块应遵循数据隐私和保护政策，确保用户数据在存储和传输过程中的安全。
- 系统应对敏感信息进行加密处理，防止未授权访问。

通过以上功能的实现，报告与分析模块将为用户提供全面的迁移过程洞察，帮助他们做出更明智的决策。
"""

    # 将Markdown转换为Word文档
    # markdown_to_word_with_template(markdown_text, 'output.docx', '../controllers/custom-reference.docx')

    # print('Word文档已生成')

    # 使用示例
    replacements = {
        '${headercontent}': 'John Doe',
        '${hi}': 'John Doe',
        '${date}': '2024-08-23',
        'hello': 'nihao'
    }
    process_docx('./controllers/test2.docx', replacements)