import logging
from datetime import datetime
from typing import List, Dict
from celery import shared_task
from flask import current_app
from services.datasource_service import DatasourceService
from extensions.ext_database import db
from models.datasource import DataSourceAppStatistics
from services.errors.exception import DBTestConnectionException
from tasks.datasource_statistics_task import update_app_statistics
from celery.exceptions import SoftTimeLimitExceeded

def get_training_data_count(app_id: str) -> int:
    """获取应用的训练数据数量"""
    try:
        # 从DataSourceAppStatistics表中获取训练数据数量
        stats = db.session.query(DataSourceAppStatistics).filter_by(app_id=app_id).first()
        return stats.training_data_count if stats else 0
    except Exception as e:
        current_app.logger.error(f"获取训练数据数量失败: {str(e)}")
        return 0

@shared_task(queue='chatdb', soft_time_limit=300)  # 设置5分钟超时
def check_app_task(app_id: str) -> List[Dict]:
    """检查应用状态的任务"""
    try:
        # 更新任务状态
        logging.info("开始执行统计任务...")
        update_app_statistics(app_id)
        logging.info("统计任务完成，开始执行检测...")
        
        # 获取应用信息
        app = DatasourceService.get_app_by_id(app_id)
        if not app:
            return []
        
        results = []
        
        # 检查初始化提示词
        if not app.initial_prompt or len(app.initial_prompt.strip()) <= 5:
            results.append({
                'type': 'warning',
                'message': '初始化提示词为空'
            })
        else:
            results.append({
                'type': 'success',
                'message': '初始化提示词正常'
            })
        
        # 检查数据源连接
        datasource = DatasourceService.get_datasource_by_id(app.datasource_id)
        if datasource:
            connection_info = {
                'type': datasource.type,
                'host': datasource.host,
                'port': datasource.port,
                'database': datasource.database,
                'username': datasource.username,
                'password': datasource.password
            }

            try:
                if DatasourceService.test_connection(connection_info):
                    results.append({
                        'type': 'success',
                        'message': '数据源连接正常'
                    })
                else:
                    results.append({
                        'type': 'error',
                        'message': '数据源连接失败'
                    })
            except DBTestConnectionException as e:
                results.append({
                    'type': 'error',
                    'message': f'数据源{e}'
                })
        else:
            results.append({
                'type': 'error',
                'message': '数据源不存在'
            })
        
        # 检查训练数据
        training_count = get_training_data_count(str(app.id))
        if training_count < 5:
            results.append({
                'type': 'warning',
                'message': f'训练数据不足，当前仅{training_count}条'
            })
        else:
            results.append({
                'type': 'success',
                'message': f'训练数据充足，共{training_count}条'
            })
        
        # 更新检测结果到数据库
        app.check_results = results
        app.last_check_time = datetime.now()
        db.session.commit()
        
        return results
        
    except SoftTimeLimitExceeded:
        logging.error("检测任务执行超时")
        return []
    except Exception as e:
        logging.error(f"应用检测失败: {str(e)}")
        return []